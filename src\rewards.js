// 定义礼物类型常量
export const GiftType = {
    SKILL: 'skill',
    EQUIP: 'equip',
    RECOVERY: 'recovery',
    ATTRIBUTE: 'attribute',
    PROBABILITY: 'probability',
    NONE: 'none'
};

// 创建通用礼物对象
export function createGift(name, type = GiftType.NONE) {
    return { name, type };
}

// 创建恢复盲盒礼物
export function createRecoveryGift(minRecover, maxRecover, text = '恢复盲盒', color = 'success') {
    return {
        type: 1, // 对应WordPage.vue中的type
        info: [minRecover, maxRecover],
        text,
        color
    };
}

// 创建属性盲盒礼物
export function createAttributeGift(maxHp, maxMp, atk, text = '属性盲盒', color = 'primary') {
    return {
        type: 2, // 对应WordPage.vue中的type
        info: [maxHp, maxMp, atk],
        text,
        color
    };
}

// 创建概率盲盒礼物
export function createProbabilityGift(rewards, text = '随机盲盒', type = 3, color = 'warning') {
    return {
        type: type, // 3 for skill, 4 for item
        info: rewards, // [{ chance: 0.1, name: 'skill_name', type: GiftType.SKILL }]
        text,
        color
    };
}

// 创建技能礼物
export function createSkillGift(chance, level, text = '技能盲盒', color = 'danger') {
    // 这是一个示例，实际可能需要更复杂的逻辑来选择技能
    // 暂时返回一个通用的概率盲盒，实际技能名在map.js中定义
    return createProbabilityGift([
        { chance: chance, name: 'taibai_j', type: GiftType.SKILL },
        { chance: chance, name: 'jiuzhang_z', type: GiftType.SKILL },
        { chance: chance, name: 'qiyi_q', type: GiftType.SKILL }
    ], text, 3, color);
}

// 创建装备礼物
export function createEquipGift(chance, level, text = '装备盲盒', color = 'warning') {
    // 这是一个示例，实际可能需要更复杂的逻辑来选择装备
    // 暂时返回一个通用的概率盲盒，实际装备名在map.js中定义
    return createProbabilityGift([
        { chance: chance, name: 'mujian_e', type: GiftType.EQUIP },
        { chance: chance, name: 'shengxiujian_e', type: GiftType.EQUIP },
        { chance: chance, name: 'dagger_e', type: GiftType.EQUIP }
    ], text, 4, color);
}

// 创建奇遇礼物（随机获得武器、防具或护符）
export function createAdventureGift() {
    const adventureRewards = [
        // 护符
        { chance: 0.1, name: 'wulingzhu_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'shulaibao_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'shuqubao_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'datongin_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'datongin_b', type: GiftType.EQUIP },
        { chance: 0.1, name: 'yuwenjiangzhuang_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'zilaibao_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'ziqubao_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'gongbushiji_a', type: GiftType.EQUIP },
        { chance: 0.1, name: 'qisehua_a', type: GiftType.EQUIP }
    ];
    
    return createProbabilityGift(adventureRewards, '奇遇宝箱', 5, 'warning');
}
