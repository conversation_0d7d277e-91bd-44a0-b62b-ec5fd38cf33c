import { getSkillByName } from './skill.js'
import { getEquipByName } from './equip.js'
export default class hero {
    constructor() {
        this.init();
    }
    init() {
        this.name = 'hero';
        this.base_atk = 20;
        this.atk = 20;
        this.base_def = 10;
        this.def = 10;
        this.base_lifesteal = 0.05;
        this.lifesteal = 0.05;
        this.baoji_rate = 0.1;
        this.weapon = 'none';
        this.protective = 'none';
        this.amulets = []; // 护符数组，最多可装备20个
        this.max_amulets = 20; // 最大护符数量
        this.money = 100; // Initial money
        this.hp = {
            value: 60,
            max: 60
        };
        this.skills =
        {
            'ack':getSkillByName('ack'),
            //'wudi':getSkillByName('wudi'),
        } //['ack', 'wudi','qiyi_q'];

        //this.props = ['buyi_p', 'xixuejian_e', 'wulingzhu_a', 'shul<PERSON><PERSON>_a', 'shuqubao_a', 'datongin_a', 'datongin_b', 'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a','shijianzha_a']; // 给玩家一个初始防具、吸血武器、无零珠、数来宝、数去宝、多面体、大同印、语文奖状、字来宝、字去宝、工部诗集和七色花（背包中）
        this.props = [];
        //this.poem_arr = ['dufu'];
        this.poem_arr = [];
    }
    init_by(_src) {
        this.name = _src.name;
        this.base_atk = _src.base_atk;
        this.atk = _src.atk;
        this.base_def = _src.base_def || 0;
        this.def = _src.def || 0;
        this.base_lifesteal = _src.base_lifesteal || 0;
        this.lifesteal = _src.lifesteal || 0;
        this.baoji_rate = _src.baoji_rate;
        this.weapon = _src.weapon;
        this.protective = _src.protective || 'none';
        this.amulets = JSON.parse(JSON.stringify(_src.amulets || []));
        this.max_amulets = _src.max_amulets || 20;
        this.hp = JSON.parse(JSON.stringify(_src.hp));//为了深拷贝，需要这么写
        this.skills = JSON.parse(JSON.stringify(_src.skills));

        this.props = JSON.parse(JSON.stringify(_src.props));
        this.poem_arr = JSON.parse(JSON.stringify(_src.poem_arr));
        this.money = _src.money; // Copy money
    }
    setName(_name) {
        this.name = _name;
    }
    addHp(_value,_info)
    {
        if (this.hp.value < this.hp.max - _value) {
            _info = '你获得了' + _value + '生命值';
            this.hp.value += _value;
        }
        else {
            _info = '你获得了' + (this.hp.max - this.hp.value) + '生命值';
            this.hp.value = this.hp.max;
        }
    }
    setHP(_hp) {
        this.hp.value = _hp;
    }

    setMaxHP(_max_hp) {
        this.hp.max = _max_hp;
    }

    addBaseATK(_atk) {
        this.base_atk += _atk;
        this.atk += _atk;
    }
    setATK(_atk) {
        this.atk = _atk;
    }
    addBaseDEF(_def) {
        this.base_def += _def;
        this.def += _def;
    }
    setDEF(_def) {
        this.def = _def;
    }
    addBaseLifesteal(_lifesteal) {
        this.base_lifesteal += _lifesteal;
        this.lifesteal += _lifesteal;
    }
    setLifesteal(_lifesteal) {
        this.lifesteal = _lifesteal;
    }
    addSkill(_skill) {
        if(this.skills.hasOwnProperty(_skill.name))
        {
            // 移除固定伤害增加，改为通过等级计算伤害加成
            this.skills[_skill.name].level+=1;
            
        }
        else{
            this.skills[_skill.name] = _skill;
        }
        console.log(this.skills);
    }
    changeWapen(_name) {
        // 卸下当前武器的属性加成
        if (this.weapon != '无' && this.weapon != 'none') {
            const oldWeapon = getEquipByName(this.weapon);
            if (oldWeapon.lifesteal) {
                this.setLifesteal(this.lifesteal - oldWeapon.lifesteal);
            }
            this.props.push(this.weapon);
        }
        // 装备新武器
        this.weapon = _name;
        if (_name != 'none') {
            const newWeapon = getEquipByName(_name);
            if (newWeapon.lifesteal) {
                this.setLifesteal(this.lifesteal + newWeapon.lifesteal);
            }
        }
    }
    gainAThing(_item) {
        let itemName = '';
        switch (_item.type) {
            case 'skill':// 添加技能
                this.addSkill(getSkillByName(_item.name));
                itemName = getSkillByName(_item.name).name;
                var add = getSkillByName(_item.name).add;
                if (add != undefined) {
                    switch (add.type) {
                        case 'poem':
                            this.poem_arr.push(add.name);
                            break;
                        default:
                            break;
                    }
                }
                break;
            case 'equip':// 更换装备
                var equip = getEquipByName(_item.name);
                if (equip.type === 'weapon') {
                    // 注释掉自动装备逻辑，直接放入背包
                    // if (equip.hurt > this.atk - this.base_atk) {
                    //     this.changeWapen(_item.name);
                    //     this.setATK(this.base_atk + equip.hurt);
                    // } else {
                    //     this.props.push(_item.name);
                    // }
                    this.props.push(_item.name);
                } else if (equip.type === 'protective') {
                    // 注释掉自动装备逻辑，直接放入背包
                    // // 卸下当前防具的属性加成
                    // if (this.protective !== 'none') {
                    //     const oldProtective = getEquipByName(this.protective);
                    //     if (oldProtective.def) {
                    //         this.setDEF(this.def - oldProtective.def);
                    //     }
                    //     if (oldProtective.hp_bonus) {
                    //         this.setMaxHP(this.hp.max - oldProtective.hp_bonus);
                    //         if (this.hp.value > this.hp.max) {
                    //             this.hp.value = this.hp.max;
                    //         }
                    //     }
                    //     this.props.push(this.protective);
                    // }
                    // // 装备新防具
                    // this.protective = _item.name;
                    // if (equip.def) {
                    //     this.setDEF(this.def + equip.def);
                    // }
                    // if (equip.hp_bonus) {
                    //     this.setMaxHP(this.hp.max + equip.hp_bonus);
                    // }
                    this.props.push(_item.name);

                } else if (equip.type === 'poem') {
                    this.poem_arr.push(equip.poemName);
                } else if (equip.type === 'amulet') {
                    // 注释掉自动装备逻辑，直接放入背包
                    // // 护符处理逻辑
                    // if (this.amulets.length < this.max_amulets) {
                    //     this.amulets.push(_item.name);
                    //     // 应用护符属性加成
                    //     if (equip.atk_bonus) {
                    //         this.setATK(this.atk + equip.atk_bonus);
                    //     }
                    //     if (equip.def_bonus) {
                    //         this.setDEF(this.def + equip.def_bonus);
                    //     }
                    //     if (equip.hp_bonus) {
                    //         this.setMaxHP(this.hp.max + equip.hp_bonus);
                    //     }
                    //     if (equip.lifesteal_bonus) {
                    //         this.setLifesteal(this.lifesteal + equip.lifesteal_bonus);
                    //     }
                    //     if (equip.crit_bonus) {
                    //         this.baoji_rate += equip.crit_bonus;
                    //     }
                    // } else {
                    //     // 护符已满，放入道具栏
                    //     this.props.push(_item.name);
                    // }
                    this.props.push(_item.name);
                }
                itemName = equip.name;
                break;
            default:
                break;
        }
        return itemName;
    }
    
    // 移除护符
    removeAmulet(amuletName) {
        const index = this.amulets.indexOf(amuletName);
        if (index > -1) {
            this.amulets.splice(index, 1);
            const amulet = getEquipByName(amuletName);
            
            // 移除护符属性加成
            if (amulet.atk_bonus) {
                this.setATK(this.atk - amulet.atk_bonus);
            }
            if (amulet.def_bonus) {
                this.setDEF(this.def - amulet.def_bonus);
            }
            if (amulet.hp_bonus) {
                this.setMaxHP(this.hp.max - amulet.hp_bonus);
                if (this.hp.value > this.hp.max) {
                    this.hp.value = this.hp.max;
                }
            }
            if (amulet.lifesteal_bonus) {
                this.setLifesteal(this.lifesteal - amulet.lifesteal_bonus);
            }
            if (amulet.crit_bonus) {
                this.baoji_rate -= amulet.crit_bonus;
            }
            
            // 将护符放回道具栏
            this.props.push(amuletName);
            return true;
        }
        return false;
    }

}
