# 游戏打点功能设置指南

本文档介绍如何设置和测试游戏的打点功能。

## 功能概述

已为游戏添加了完整的打点功能，包括：

1. **游戏开始打点** (type=1): 当玩家点击"开始游戏"按钮时触发
2. **重新开始打点** (type=2): 当玩家死亡后选择装备重新开始时触发

## 设置步骤

### 1. 设置MySQL数据库

首先需要安装并配置MySQL数据库：

```bash
# 创建数据库
mysql -u root -p
```

```sql
CREATE DATABASE word_game_analytics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

或者直接运行提供的SQL文件：
```bash
mysql -u root -p < analytics-server/database.sql
```

### 2. 配置Go服务器

修改 `analytics-server/main.go` 中的数据库连接字符串：

```go
dsn := "root:你的密码@tcp(localhost:3306)/word_game_analytics?charset=utf8mb4&parseTime=True&loc=Local"
```

### 3. 启动Go服务器

```bash
cd analytics-server
go mod tidy
go run main.go
```

服务器将在 `http://localhost:8081` 启动。

### 4. 启动前端项目

```bash
npm run dev
```

前端将在 `http://localhost:8080` 启动。

## 测试步骤

### 1. 测试服务器健康状态

访问 `http://localhost:8081/health` 确认服务器正常运行。

### 2. 测试游戏开始打点

1. 打开游戏首页 `http://localhost:8080`
2. 点击"进入"按钮
3. 检查浏览器控制台，应该看到类似信息：
   ```
   Tracking event: {type: 1, source: "web", level: 0, extra: {...}}
   Analytics event sent successfully: {success: true, id: 1, message: "Event recorded successfully"}
   ```

### 3. 测试重新开始打点

1. 进入游戏并开始战斗
2. 让角色死亡（可以通过不操作让敌人攻击）
3. 在死亡页面选择装备并重新开始
4. 检查浏览器控制台，应该看到类似信息：
   ```
   Tracking event: {type: 2, source: "web", level: 3, extra: {...}}
   Analytics event sent successfully: {success: true, id: 2, message: "Event recorded successfully"}
   ```

### 4. 查看数据库记录

```sql
USE word_game_analytics;
SELECT * FROM analytics_events ORDER BY timestamp DESC;
```

### 5. 查看统计信息

访问 `http://localhost:8081/api/analytics/stats` 查看统计数据。

## 打点数据说明

### 游戏开始事件 (type=1)

```json
{
  "type": 1,
  "source": "web",
  "level": 0,
  "extra": {
    "event_name": "game_start",
    "button_clicked": "start_game",
    "page": "main_start",
    "game_version": "1.0.0",
    "userAgent": "Mozilla/5.0...",
    "language": "zh-CN",
    "platform": "Win32",
    "screenResolution": "1920x1080",
    "timezone": "Asia/Shanghai",
    "referrer": "direct",
    "start_time": "2023-12-01T10:00:00.000Z"
  }
}
```

### 重新开始事件 (type=2)

```json
{
  "type": 2,
  "source": "web",
  "level": 3,
  "extra": {
    "event_name": "game_restart",
    "selected_equipment": "sword_1",
    "equipment_type": "weapon",
    "death_map_idx": 1000,
    "death_data_idx": "3",
    "hero_level": 1,
    "hero_hp": 100,
    "hero_atk": 10,
    "hero_def": 5,
    "hero_money": 50,
    "equipped_weapon": "none",
    "equipped_armor": "none",
    "amulets_count": 0,
    "death_level": 3,
    "restart_time": "2023-12-01T10:05:00.000Z"
  }
}
```

## 故障排除

### 1. 数据库连接失败

- 检查MySQL是否正在运行
- 确认数据库连接字符串中的用户名、密码、数据库名是否正确
- 确认数据库已创建

### 2. CORS错误

- 确认Go服务器的CORS配置包含前端域名
- 检查前端是否运行在 `http://localhost:8080`

### 3. 打点请求失败

- 检查Go服务器是否在8081端口运行
- 查看浏览器网络面板确认请求状态
- 检查服务器日志

### 4. 前端控制台错误

- 确认 `analytics.js` 文件已正确导入
- 检查是否有JavaScript语法错误

## 扩展功能

可以根据需要添加更多打点事件：

1. **关卡完成事件**: 玩家完成某个关卡时
2. **装备获得事件**: 玩家获得新装备时
3. **技能使用事件**: 玩家使用技能时
4. **商店购买事件**: 玩家在商店购买物品时

只需要在相应的Vue组件中调用 `analytics.track()` 方法即可。
