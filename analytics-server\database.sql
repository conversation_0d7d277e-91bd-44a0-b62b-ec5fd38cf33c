-- 创建数据库
CREATE DATABASE IF NOT EXISTS word_game_analytics 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE word_game_analytics;

-- 创建打点事件表
CREATE TABLE IF NOT EXISTS analytics_events (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    type INT NOT NULL COMMENT '事件类型: 1=开始游戏, 2=重新开始',
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '打点时间',
    source VARCHAR(255) DEFAULT '' COMMENT '来源信息（如web、mobile等）',
    level INT DEFAULT 0 COMMENT '死亡关卡（type=2时有效，记录玩家死亡时的关卡）',
    user_agent TEXT COMMENT '用户代理字符串',
    ip VARCHAR(45) COMMENT '用户IP地址（支持IPv6）',
    extra JSON COMMENT '额外信息（JSON格式，可扩展）',
    
    -- 索引优化查询性能
    INDEX idx_type (type),
    INDEX idx_timestamp (timestamp),
    INDEX idx_level (level),
    INDEX idx_type_timestamp (type, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏打点事件表';

-- 插入示例数据（可选）
INSERT INTO analytics_events (type, source, level, user_agent, ip, extra) VALUES
(1, 'web', 0, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '127.0.0.1', '{"browser": "Chrome", "version": "1.0.0"}'),
(2, 'web', 3, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '127.0.0.1', '{"browser": "Chrome", "death_reason": "enemy_attack"}'),
(1, 'web', 0, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '*************', '{"browser": "Safari", "version": "1.0.0"}');

-- 创建统计视图（可选）
CREATE OR REPLACE VIEW analytics_summary AS
SELECT 
    DATE(timestamp) as date,
    type,
    COUNT(*) as event_count,
    COUNT(DISTINCT ip) as unique_users
FROM analytics_events 
GROUP BY DATE(timestamp), type
ORDER BY date DESC, type;

-- 查询示例

-- 1. 获取今日统计
SELECT 
    type,
    COUNT(*) as count,
    CASE 
        WHEN type = 1 THEN '开始游戏'
        WHEN type = 2 THEN '重新开始'
        ELSE '未知'
    END as type_name
FROM analytics_events 
WHERE DATE(timestamp) = CURDATE()
GROUP BY type;

-- 2. 获取最近7天的每日统计
SELECT 
    DATE(timestamp) as date,
    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as game_starts,
    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as game_restarts,
    COUNT(DISTINCT ip) as unique_users
FROM analytics_events 
WHERE timestamp >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(timestamp)
ORDER BY date DESC;

-- 3. 获取死亡关卡分布（type=2的事件）
SELECT 
    level,
    COUNT(*) as death_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM analytics_events WHERE type = 2), 2) as percentage
FROM analytics_events 
WHERE type = 2 AND level > 0
GROUP BY level
ORDER BY level;

-- 4. 获取用户活跃度（按IP统计）
SELECT 
    ip,
    COUNT(*) as total_events,
    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as starts,
    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as restarts,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen
FROM analytics_events 
GROUP BY ip
HAVING total_events > 1
ORDER BY total_events DESC
LIMIT 10;
