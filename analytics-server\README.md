# Word Game Analytics Server

这是一个用Go语言编写的游戏打点服务器，用于收集和存储游戏中的用户行为数据。

## 功能特性

- 接收游戏开始和重新开始的打点事件
- 存储事件到MySQL数据库
- 提供统计信息API
- 支持CORS跨域请求
- 健康检查接口

## 环境要求

- Go 1.21+
- MySQL 5.7+

## 安装和运行

### 1. 安装依赖

```bash
cd analytics-server
go mod tidy
```

### 2. 设置MySQL数据库

创建数据库：
```sql
CREATE DATABASE word_game_analytics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

修改 `main.go` 中的数据库连接字符串：
```go
dsn := "用户名:密码@tcp(localhost:3306)/word_game_analytics?charset=utf8mb4&parseTime=True&loc=Local"
```

### 3. 运行服务器

```bash
go run main.go
```

服务器将在 `http://localhost:8081` 启动。

## API接口

### 1. 发送打点事件

**POST** `/api/analytics`

请求体：
```json
{
  "type": 1,           // 1: 开始游戏, 2: 重新开始
  "source": "web",     // 来源信息
  "level": 5,          // 死亡关卡（type=2时有效）
  "extra": {           // 额外信息
    "browser": "Chrome",
    "version": "1.0.0"
  }
}
```

响应：
```json
{
  "success": true,
  "id": 123,
  "message": "Event recorded successfully"
}
```

### 2. 获取统计信息

**GET** `/api/analytics/stats`

响应：
```json
{
  "total_events": 1000,
  "by_type": {
    "game_start": 600,
    "game_restart": 400
  },
  "today_events": 50
}
```

### 3. 健康检查

**GET** `/health`

响应：
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 数据库表结构

```sql
CREATE TABLE analytics_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type INT NOT NULL COMMENT '事件类型: 1=开始游戏, 2=重新开始',
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '打点时间',
    source VARCHAR(255) DEFAULT '' COMMENT '来源信息',
    level INT DEFAULT 0 COMMENT '死亡关卡（type=2时有效）',
    user_agent TEXT COMMENT '用户代理',
    ip VARCHAR(45) COMMENT '用户IP',
    extra JSON COMMENT '额外信息',
    INDEX idx_type (type),
    INDEX idx_timestamp (timestamp),
    INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 开发说明

- 服务器默认运行在8081端口
- 支持来自localhost:8080的跨域请求（前端开发服务器）
- 所有事件都会记录时间戳、IP地址和User-Agent
- 额外信息以JSON格式存储，便于扩展
