<template>
  <div class="app">
    <!--游戏起始页-->
    <MainStart v-show="show_page==100" @childEvent="show_page_change"/>
     <!--第一关起始页-->
    <SceneTitle v-show="show_page==101" @childEvent="show_page_change"/>
    <!--故事描述页-->
    <WordPage v-show="show_page==201" @childEvent="show_page_change"/>

    <div id="footer" v-show="show_page==100" class = 'footer-fixed' style="text-align: center;"><a href="https://beian.miit.gov.cn" target="_blank">京ICP备2022003550号</a></div>
  </div>
</template>

<script>
import MainStart from './MainStart.vue'
import SceneTitle from './SceneTitle.vue'
import WordPage from './WordPage.vue'
export default {
  name: 'app',
  data () {
    return {
      show_page: 100, 
    }
  },
  components: {
    MainStart,
    SceneTitle,
    WordPage,
  },
  methods:{
    show_page_change(page_idx)
    {
      this.show_page = page_idx;
      switch(page_idx)
      {
        case 101://SceneTitle
        {
          setTimeout(this.show_page_change,1000,201); // 场景起始页在3s后自动切走
        }
      }
    },
    
  },
  
}
</script>
