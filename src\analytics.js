/**
 * 游戏打点工具类
 * 用于发送用户行为数据到分析服务器
 */

class Analytics {
    constructor() {
        // 打点服务器地址
        this.baseURL = 'http://localhost:8081/api/analytics';
        
        // 默认来源信息
        this.defaultSource = 'web';
        
        // 是否启用打点（可以通过环境变量控制）
        this.enabled = true;
        
        // 请求超时时间（毫秒）
        this.timeout = 5000;
        
        // 重试次数
        this.maxRetries = 2;
        
        // 缓存队列（网络异常时暂存）
        this.eventQueue = [];
        
        // 初始化时获取一些基础信息
        this.sessionInfo = this.getSessionInfo();
        
        console.log('Analytics initialized:', this.sessionInfo);
    }
    
    /**
     * 获取会话信息
     */
    getSessionInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screenResolution: `${screen.width}x${screen.height}`,
            timestamp: new Date().toISOString(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            referrer: document.referrer || 'direct'
        };
    }
    
    /**
     * 发送打点事件
     * @param {number} type - 事件类型 (1: 开始游戏, 2: 重新开始)
     * @param {Object} options - 可选参数
     * @param {string} options.source - 来源信息
     * @param {number} options.level - 关卡信息（死亡时的关卡）
     * @param {Object} options.extra - 额外信息
     */
    async track(type, options = {}) {
        if (!this.enabled) {
            console.log('Analytics disabled, skipping event:', type);
            return;
        }
        
        // 验证事件类型
        if (type !== 1 && type !== 2) {
            console.error('Invalid analytics event type:', type);
            return;
        }
        
        // 构建事件数据
        const eventData = {
            type: type,
            source: options.source || this.defaultSource,
            level: options.level || 0,
            extra: {
                ...this.sessionInfo,
                ...options.extra
            }
        };
        
        // 添加事件特定的额外信息
        if (type === 1) {
            eventData.extra.event_name = 'game_start';
        } else if (type === 2) {
            eventData.extra.event_name = 'game_restart';
            if (options.level > 0) {
                eventData.extra.death_level = options.level;
            }
        }
        
        console.log('Tracking event:', eventData);
        
        try {
            await this.sendEvent(eventData);
        } catch (error) {
            console.error('Failed to send analytics event:', error);
            // 将失败的事件加入队列，稍后重试
            this.eventQueue.push(eventData);
            this.scheduleRetry();
        }
    }
    
    /**
     * 发送游戏开始事件
     * @param {Object} extra - 额外信息
     */
    trackGameStart(extra = {}) {
        return this.track(1, {
            extra: {
                ...extra,
                start_time: new Date().toISOString()
            }
        });
    }
    
    /**
     * 发送游戏重新开始事件
     * @param {number} deathLevel - 死亡时的关卡
     * @param {Object} extra - 额外信息
     */
    trackGameRestart(deathLevel = 0, extra = {}) {
        return this.track(2, {
            level: deathLevel,
            extra: {
                ...extra,
                restart_time: new Date().toISOString(),
                death_level: deathLevel
            }
        });
    }
    
    /**
     * 发送HTTP请求到分析服务器
     * @param {Object} eventData - 事件数据
     */
    async sendEvent(eventData) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        
        try {
            const response = await fetch(this.baseURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(eventData),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log('Analytics event sent successfully:', result);
            return result;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }
            
            throw error;
        }
    }
    
    /**
     * 安排重试发送失败的事件
     */
    scheduleRetry() {
        if (this.retryTimeout) {
            return; // 已经有重试计划了
        }
        
        this.retryTimeout = setTimeout(() => {
            this.retryFailedEvents();
            this.retryTimeout = null;
        }, 10000); // 10秒后重试
    }
    
    /**
     * 重试发送失败的事件
     */
    async retryFailedEvents() {
        if (this.eventQueue.length === 0) {
            return;
        }
        
        console.log(`Retrying ${this.eventQueue.length} failed analytics events`);
        
        const eventsToRetry = [...this.eventQueue];
        this.eventQueue = [];
        
        for (const eventData of eventsToRetry) {
            try {
                await this.sendEvent(eventData);
            } catch (error) {
                console.error('Retry failed for event:', eventData, error);
                // 如果重试仍然失败，可以选择丢弃或再次加入队列
                // 这里选择丢弃以避免无限重试
            }
        }
    }
    
    /**
     * 获取分析服务器状态
     */
    async getServerHealth() {
        try {
            const response = await fetch('http://localhost:8081/health');
            if (response.ok) {
                return await response.json();
            }
            throw new Error(`Server health check failed: ${response.status}`);
        } catch (error) {
            console.error('Analytics server health check failed:', error);
            return null;
        }
    }
    
    /**
     * 设置是否启用打点
     * @param {boolean} enabled 
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        console.log('Analytics enabled:', enabled);
    }
    
    /**
     * 清空事件队列
     */
    clearQueue() {
        this.eventQueue = [];
        console.log('Analytics event queue cleared');
    }
}

// 创建全局实例
const analytics = new Analytics();

// 导出实例和类
export default analytics;
export { Analytics };
