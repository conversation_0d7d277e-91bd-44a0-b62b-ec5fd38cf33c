<script>
import { getMapdata } from './map.js';
import { getSkillByName, getPoemObj, getJiuzhangSource, getQiyiSource } from './skill.js'
import { getEnemyByName } from './enemy.js'
import { getEquipByName } from './equip.js'
import hero from './hero.js';
import { Message } from 'element-ui';
import DeathPage from './DeathPage.vue';

function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
function sleep_block(ms) {
    var start = new Date().getTime()
    while (new Date().getTime() < start + ms);
}
export default {
    name: 'WordPage',
    components: {
        DeathPage
    },
    data() {
        return {
            mapData: [{
                paraList: [], //文字数组
                selectList: [], //事件选择数组
                roadList: [], //道路数组
            }],
            dataIdx: 0,//map 的小节
            mapIdx: 1000,
            activeTab: 'first',
            activeNames: ['1'], // 默认展开第一个面板
            show_div: 'story',
            show_skill: 'none',
            select_skill: {},
            tips_info: '开始战斗！（请点击上方的技能）',
            show_tips: true,
            round_info: '【你的回合】',
            enemy_list: [],
            taibai_obj: {
                replaceStrArr: [''],
                posList: [0],
                blankList: [''],
                replaceStrArr: [''],
                anserList: [''],
                blurList: [''],

            },
            jiuzhang_obj: {
                total: 0,
                leftList: [],
                anserList: [],
            },
            qiyiquan_obj: {
                colorSequence: [],
                answerList: [],
                allColors: [],
                showColors: true,
                currentAnswerIndex: 0
            },
            poem_anser_index: 0,
            jiuzhang_anser_index: 0,
            poem_correct: [true, true],
            jiuzhang_correct: [],
            bFighting: false,
            percentage: 100,
            intervalHandler: 0,
            hero: new hero(),
            // 敌人队列管理
            enemyQueue: [], // 待出场的敌人队列
            currentBatchSize: 3, // 每批最多3个敌人
            defeatedEliteCount: 0, // 击败的精英怪数量
            // 动画状态控制
            playerAttacking: false,
            playerTakingDamage: false,
            enemyTakingDamage: false,
            enemyAttacking: false,
            // 回合状态控制
            isPlayerTurn: true,
            currentAttackingEnemyIndex: -1,
            attackingEnemyId: null, // 当前攻击的敌人ID
            // 道具简介弹窗
            itemDetailDialog: false,
            selectedItem: null,
            // 奇遇宝箱状态
            adventureTreasureOpened: false,
            // 死亡页面状态
            showDeathPage: false,
            deadHeroData: null
        }
    },
    created() {
        this.toMap(this.mapIdx);
    },
    methods: {
        // 设置提示信息并显示提示框
        setTipsInfo(message) {
            this.tips_info = message;
            this.show_tips = true;
        },
        // 跳转到对应div
        to_div(item) {
            if (!item || item.to_id === undefined) {
                console.error('无效的跳转目标:', item);
                return;
            }

            // 确保ID类型一致性 - 转换为字符串进行查找
            const targetId = String(item.to_id);
            if (!this.mapData[targetId]) {
                console.error('无效的跳转目标:', item, '可用的场景ID:', Object.keys(this.mapData));
                return;
            }

            this.dataIdx = targetId;
            this.show_div = this.mapData[this.dataIdx].type;
            
            // If navigating to a shop, set activeTab to 'shop'
            if (this.show_div === 'shop') {
                this.activeTab = 'shop';
            } else {
                this.activeTab = 'first'; // Default to story/road tab
            }
            
            // 重置奇遇宝箱状态
            this.adventureTreasureOpened = false;

            // 获取技能或装备
            if (item.gift) {
                this.hero.gainAThing(item.gift);
            }
            
            // 奇遇页面不再自动获得礼物，改为手动点击宝箱按钮

            if (this.show_div === 'fight') {
                // 初始化敌人队列
                this.enemyQueue = [];
                for (let item1 of this.mapData[this.dataIdx].enemyList || []) {
                    this.enemyQueue.push(this.getEnemyByName(item1));
                }
                
                // 显示第一批敌人（最多3个）
                this.enemy_list = [];
                this.spawnNextBatch();
                
                this.bFighting = true;
                // 重置战斗相关状态
                this.defeatedEliteCount = 0; // 重置精英怪计数器
                this.show_skill = 'none';  // 添加这行，确保战斗开始时不显示技能选择界面
                this.tips_info = '开始战斗！（请点击上方的技能）';
                this.round_info = '【你的回合】';
                this.show_tips = true; // 战斗开始时显示提示
                
                // 玩家回合期间持续显示绿色边框
                this.playerAttacking = true;
            }
        },
        // 跳转到对应map
        toMap(mapIdx) {
            // 获取当前map的数据,将其保存到data数组
            // Hero reset is now handled by resetGame() on death, no longer needed here based on mapIdx
            const newMapData = getMapdata(mapIdx);
            // 添加数据验证
            if (!Array.isArray(newMapData) || newMapData.length === 0) {
                console.error('无效的地图数据:', mapIdx);
                return;
            }

            // 将数组转换为以ID为键的对象，便于快速索引
            const mapDataObj = {};
            newMapData.forEach(scene => {
                mapDataObj[String(scene.id)] = scene;
            });
            this.mapData = mapDataObj;
            
            // 找到第一个场景的ID，确保类型一致性
            const firstSceneId = String(newMapData[0].id);
            this.show_div = this.mapData[firstSceneId].type;
            this.dataIdx = firstSceneId;

            // If navigating to a shop, set activeTab to 'shop'
            if (this.show_div === 'shop') {
                this.activeTab = 'shop';
            } else {
                this.activeTab = 'first'; // Default to story/road tab
            }
        },
        // 获取敌人
        getEnemyByName(_name) {
            const enemy = getEnemyByName(_name);
            // 确保敌人对象有必要的属性
            if (!enemy || !enemy.name || !enemy.hp) {
                console.error('获取敌人失败:', _name, enemy);
                return {
                    name: '错误敌人',
                    hp: { value: 1, max: 1 },
                    atk: 1
                };
            }
            return enemy;
        },
        // 获取武器名
        getEquipName(_name) {
            const equip = getEquipByName(_name);
            return equip ? equip.name : '无';
        },
        // 获取武器
        getEquipByName(_name) {
            return getEquipByName(_name);
        },
        // 获取敌人批次信息
        getEnemyBatchInfo() {
            const totalEnemies = this.enemy_list.length + this.enemyQueue.length;
            const totalBatches = Math.ceil(totalEnemies / this.currentBatchSize);
            const currentBatch = Math.ceil((totalEnemies - this.enemyQueue.length) / this.currentBatchSize);
            return `(${currentBatch}/${totalBatches})`;
        },
        // 获取护符装备按钮是否禁用
        getAmuletEquipDisabled(amuletKey) {
            const canRepeatEquip = ['wulingzhu_a', 'shulaibao_a', 'shuqubao_a', 'datongin_a', 'datongin_b', 'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a'];
            
            if (canRepeatEquip.includes(amuletKey)) {
                // 可重复装备的护符只需检查护符栏是否已满
                return this.hero.amulets.length >= this.hero.max_amulets;
            } else {
                // 不可重复装备的护符需检查是否已装备或护符栏已满
                return this.hero.amulets.includes(amuletKey) || this.hero.amulets.length >= this.hero.max_amulets;
            }
        },
        // 获取护符装备按钮文本
        getAmuletEquipButtonText(amuletKey) {
            const canRepeatEquip = ['wulingzhu_a', 'shulaibao_a', 'shuqubao_a', 'datongin_a', 'datongin_b', 'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a'];
            
            if (canRepeatEquip.includes(amuletKey)) {
                // 可重复装备的护符
                if (this.hero.amulets.length >= this.hero.max_amulets) {
                    return '护符已满';
                } else {
                    const count = this.hero.amulets.filter(a => a === amuletKey).length;
                    return count > 0 ? `装备(${count})` : '装备';
                }
            } else {
                // 不可重复装备的护符
                if (this.hero.amulets.includes(amuletKey)) {
                    return '已装备';
                } else if (this.hero.amulets.length >= this.hero.max_amulets) {
                    return '护符已满';
                } else {
                    return '装备';
                }
            }
        },
        // 初始化太白剑诀
        initTaibaiInfo() {
            this.taibai_obj = getPoemObj(this.hero.poem_arr);
            
            // 检查是否装备了字来宝，增加填空数量
            const zilaibaoCount = this.hero.amulets.filter(amulet => amulet === 'zilaibao_a').length;
            // 检查是否装备了字去宝，减少填空数量
            const ziqubaoCount = this.hero.amulets.filter(amulet => amulet === 'ziqubao_a').length;
            
            // 计算最终填空数量：基础2个 + 字来宝数量 - 字去宝数量，至少保留1个
            const baseBlanks = this.taibai_obj.blankList.length;
            const targetBlanks = Math.max(1, baseBlanks + zilaibaoCount - ziqubaoCount);
            
            if (targetBlanks > baseBlanks) {
                // 需要增加填空（字来宝效果）
                const poem = this.taibai_obj.poem;
                const additionalBlanks = targetBlanks - baseBlanks;
                
                // 从诗句中随机选择更多字符作为填空
                const availablePositions = [];
                for (let i = 0; i < poem.length; i++) {
                    const char = poem[i];
                    // 排除标点符号和已经是填空的位置
                    if (char !== '，' && char !== '。' && char !== ' ' && !this.taibai_obj.posList.includes(i)) {
                        availablePositions.push(i);
                    }
                }
                
                // 随机选择额外的填空位置
                for (let i = 0; i < additionalBlanks && availablePositions.length > 0; i++) {
                    const randomIndex = Math.floor(Math.random() * availablePositions.length);
                    const selectedPos = availablePositions.splice(randomIndex, 1)[0];
                    const selectedChar = poem[selectedPos];
                    
                    this.taibai_obj.posList.push(selectedPos);
                    this.taibai_obj.blankList.push(selectedChar);
                    
                    // 确保新的填空字符在选项中
                    if (!this.taibai_obj.blurList.includes(selectedChar)) {
                        this.taibai_obj.blurList.push(selectedChar);
                    }
                }
                
                // 重新排序位置列表以保持正确的显示顺序
                const sortedIndices = this.taibai_obj.posList.map((pos, index) => ({ pos, index })).sort((a, b) => a.pos - b.pos);
                const newPosList = sortedIndices.map(item => item.pos);
                const newBlankList = sortedIndices.map(item => this.taibai_obj.blankList[item.index]);
                
                this.taibai_obj.posList = newPosList;
                this.taibai_obj.blankList = newBlankList;
                
                // 重新生成replaceStrArr
                this.taibai_obj.replaceStrArr = [];
                let lastPos = 0;
                for (let i = 0; i < newPosList.length; i++) {
                    this.taibai_obj.replaceStrArr.push(poem.substring(lastPos, newPosList[i]));
                    lastPos = newPosList[i] + 1;
                }
                this.taibai_obj.replaceStrArr.push(poem.substring(lastPos));
                
                // 重新生成anserList
                this.taibai_obj.anserList = new Array(newBlankList.length).fill('__');
                
            } else if (targetBlanks < baseBlanks) {
                // 需要减少填空（字去宝效果）
                const blanksToRemove = baseBlanks - targetBlanks;
                const indicesToRemove = [];
                while (indicesToRemove.length < blanksToRemove) {
                    const randomIndex = Math.floor(Math.random() * baseBlanks);
                    if (!indicesToRemove.includes(randomIndex)) {
                        indicesToRemove.push(randomIndex);
                    }
                }
                
                // 重新构建填空数据
                const newPosList = [];
                const newBlankList = [];
                for (let i = 0; i < baseBlanks; i++) {
                    if (!indicesToRemove.includes(i)) {
                        newPosList.push(this.taibai_obj.posList[i]);
                        newBlankList.push(this.taibai_obj.blankList[i]);
                    }
                }
                
                this.taibai_obj.posList = newPosList;
                this.taibai_obj.blankList = newBlankList;
                
                // 重新生成replaceStrArr
                const poem = this.taibai_obj.poem;
                this.taibai_obj.replaceStrArr = [];
                let lastPos = 0;
                for (let i = 0; i < newPosList.length; i++) {
                    this.taibai_obj.replaceStrArr.push(poem.substring(lastPos, newPosList[i]));
                    lastPos = newPosList[i] + 1;
                }
                this.taibai_obj.replaceStrArr.push(poem.substring(lastPos));
                
                // 重新生成anserList
                this.taibai_obj.anserList = new Array(newBlankList.length).fill('__');
            }
            
            this.poem_anser_index = 0;
            this.poem_correct = new Array(this.taibai_obj.blankList.length).fill(true);
        },
        // 初始化九章掌法
        initJiuzhangInfo() {
            // 检查是否装备了无零珠
            const hasWulingzhu = this.hero.amulets.includes('wulingzhu_a');
            // 计算装备数来宝的数量
            const shulaibaoCount = this.hero.amulets.filter(amulet => amulet === 'shulaibao_a').length;
            // 计算装备数去宝的数量
            const shuqubaoCount = this.hero.amulets.filter(amulet => amulet === 'shuqubao_a').length;
            // 基础填空数量4 + 数来宝数量 - 数去宝数量，最少保持1个填空
            const slotCount = Math.max(1, 4 + shulaibaoCount - shuqubaoCount);
            
            this.jiuzhang_obj = getJiuzhangSource(slotCount, hasWulingzhu);
            this.jiuzhang_correct = new Array(slotCount).fill(true);
            this.jiuzhang_anser_index = 0;
        },
        // 初始化七益拳
        initQiyiInfo() {
            // 计算七色花数量，每个七色花增加1个填空
            const qisehuaCount = this.hero.amulets.filter(amulet => amulet === 'qisehua_a').length;
            const slotCount = 4 + qisehuaCount; // 基础4个填空 + 七色花数量
            
            this.qiyiquan_obj = getQiyiSource(slotCount);
            this.qiyiquan_obj.showColors = true;
            this.qiyiquan_obj.currentAnswerIndex = 0;
        },
        // 获取九章掌法的数字按钮数组
        getJiuzhangNumbers() {
            const hasWulingzhu = this.hero.amulets.includes('wulingzhu_a');
            if (hasWulingzhu) {
                return [1, 2, 3, 4, 5, 6, 7, 8, 9]; // 移除0
            } else {
                return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]; // 包含0
            }
        },
        // 选择要使用的技能
        selectSkill(_skill) {
            console.log('selectSkill调用:', {
                skill: _skill.name,
                bFighting: this.bFighting,
                isPlayerTurn: this.isPlayerTurn,
                canSelect: this.bFighting && this.isPlayerTurn
            });
            
            if (!this.bFighting || !this.isPlayerTurn) {
                console.log('技能选择被阻止:', {
                    bFighting: this.bFighting,
                    isPlayerTurn: this.isPlayerTurn
                });
                return;
            }
            clearInterval(this.intervalHandler);
            this.percentage = 100;//重置进度条
            this.show_skill = _skill.yName;
            this.select_skill = _skill;
            this.setTipsInfo('你开始发动\'' + _skill.name + '\'！');
            switch (_skill.yName) {
                case 'taibai_j':
                    this.initTaibaiInfo();
                    this.tips_info += '   请依此选出空位上正确的汉字！';
                    this.countdown(5);
                    break;
                case 'jiuzhang_z':
                    this.initJiuzhangInfo();
                    this.tips_info += '   请选择正确的数字以使等式成立！';
                    this.countdown(5);
                    break;
                case 'qiyi_q':
                    this.initQiyiInfo();
                    this.tips_info += '   请记住下面方块的颜色出现的顺序！';
                    // 3秒后隐藏颜色方块并开始倒计时
                    setTimeout(() => {
                        this.qiyiquan_obj.showColors = false;
                        this.tips_info = '请按照刚才颜色出现的顺序填入空格，填对越多伤害越高！';
                        this.countdown(7); // 从选项出现后开始倒计时
                    }, 3000);
                    break;
                case 'ack':
                    this.useSkill(_skill, 1);
                    break;
                case 'wudi':
                    this.useSkill(_skill, 1);
                default:
                    break;
            }
            this.bFighting = false;//发动一个技能期间不能切换其它技能,直到敌人下一次攻击将其唤醒||下一次战斗
        },
        // 使用技能
        useSkill(_skill, _success) {
            if (_success <= 0) return;
            
            // 触发玩家攻击动画
            this.playerAttacking = true;
            setTimeout(() => {
                this.playerAttacking = false;
            }, 600);
            
            var all_enemy = this.enemy_list;
            // 计算技能等级加成（每级增加30%伤害）
            const levelBonus = 1 + (_skill.level - 1) * 0.3;
            var hurt = parseInt(_skill.hurt * _success * levelBonus) + this.hero.atk;
            
            // 九章掌法的护符效果
            if (_skill.name === '九章掌法') {
                let totalDamageBonus = 1;
                
                // 无零珠效果：伤害提升15%
                const wulingzhuCount = this.hero.amulets.filter(amulet => amulet === 'wulingzhu_a').length;
                if (wulingzhuCount > 0) {
                    totalDamageBonus += wulingzhuCount * 0.15; // 每个无零珠增加15%伤害
                }
                
                // 数来宝效果：伤害提升20%
                const shulaibaoCount = this.hero.amulets.filter(amulet => amulet === 'shulaibao_a').length;
                if (shulaibaoCount > 0) {
                    totalDamageBonus += shulaibaoCount * 0.20; // 每个数来宝增加20%伤害
                }
                
                // 数去宝效果：伤害降低5%
                const shuqubaoCount = this.hero.amulets.filter(amulet => amulet === 'shuqubao_a').length;
                if (shuqubaoCount > 0) {
                    totalDamageBonus -= shuqubaoCount * 0.05; // 每个数去宝减少5%伤害
                }
                
                // 字来宝效果：伤害提升20%
                const zilaibaoCount = this.hero.amulets.filter(amulet => amulet === 'zilaibao_a').length;
                if (zilaibaoCount > 0) {
                    totalDamageBonus += zilaibaoCount * 0.20; // 每个字来宝增加20%伤害
                }
                
                // 工部诗集效果：伤害提升30%
                const gongbushijiCount = this.hero.amulets.filter(amulet => amulet === 'gongbushiji_a').length;
                if (gongbushijiCount > 0) {
                    totalDamageBonus += gongbushijiCount * 0.20; // 每个工部诗集增加20%伤害
                }
                
                // 时间炸弹效果：剩余时间转化为伤害
                const shijianzhaCount = this.hero.amulets.filter(amulet => amulet === 'shijianzha_a').length;
                if (shijianzhaCount > 0) {
                    const remainingTimeBonus = (this.percentage / 100) * shijianzhaCount * 0.01; // 每秒剩余时间增加1%伤害
                    totalDamageBonus += remainingTimeBonus;
                }
                
                hurt = Math.floor(hurt * totalDamageBonus);
            }
            // 选择敌人队列
            var select_enemy = [];
            if (_skill.objType == 'all') {
                select_enemy = all_enemy;
            } else {
                for (let item1 of all_enemy) {
                    if (item1.hp.value > 0) {
                        select_enemy.push(item1);
                        break;
                    }
                }
            }

            // 制造伤害
            const chuanciCount = this.hero.amulets.filter(amulet => amulet === 'chuancizhen_a').length;
            
            if (chuanciCount > 0 && _skill.objType !== 'all') {
                // 穿刺针效果：溢出伤害转移
                let remainingDamage = hurt;
                const aliveEnemies = all_enemy.filter(enemy => enemy.hp.value > 0);
                
                for (let i = 0; i < aliveEnemies.length && remainingDamage > 0; i++) {
                    const enemy = aliveEnemies[i];
                    if (enemy.hp.value >= remainingDamage) {
                        enemy.hp.value -= remainingDamage;
                        remainingDamage = 0;
                    } else {
                        remainingDamage -= enemy.hp.value;
                        enemy.hp.value = 0;
                    }
                }
            } else {
                // 普通伤害处理
                select_enemy.forEach(item => {
                    if (item.hp.value > hurt) {
                        item.hp.value -= hurt;
                    }
                    else {
                        item.hp.value = 0;
                    }
                });
            }
            
            // 触发敌人受伤动画
            setTimeout(() => {
                this.enemyTakingDamage = true;
                setTimeout(() => {
                    this.enemyTakingDamage = false;
                }, 500);
            }, 300);

            let damageInfo = '你发动了' + _skill.name + '(' + _success * 100 + '%)!';
            
            if (chuanciCount > 0 && _skill.objType !== 'all') {
                const killedEnemies = all_enemy.filter(enemy => enemy.hp.value === 0).length;
                if (killedEnemies > 1) {
                    damageInfo += '穿刺针效果触发！造成' + hurt + '点穿刺伤害，击杀了' + killedEnemies + '个敌人！';
                } else {
                    damageInfo += (_skill.objType == 'all' ? '全部' : '单个') + all_enemy[0].name + '受到' + hurt + '点伤害！';
                }
            } else {
                damageInfo += (_skill.objType == 'all' ? '全部' : '单个') + all_enemy[0].name + '受到' + hurt + '点伤害！';
            }
            
            this.tips_info = damageInfo;

            // 吸血效果
            if (this.hero.lifesteal > 0) {
                const lifestealHeal = Math.floor(hurt * this.hero.lifesteal);
                if (lifestealHeal > 0) {
                    this.hero.addHp(lifestealHeal);
                    this.tips_info += `吸血回复了${lifestealHeal}点生命值！`;
                }
            }

            // 七益拳治疗效果
            if (_skill.healPercent != undefined) {
                const healAmount = Math.floor(hurt * _skill.healPercent);
                if (healAmount > 0) {
                    this.hero.addHp(healAmount);
                    this.tips_info += `恢复了${healAmount}点生命值！`;
                }
            }

            if (_skill.treat != undefined) {
                this.hero.addHp(_skill.treat);
                this.tips_info += "你回复了" + _skill.treat + "点体力！";
            }

            var all_die = true;
            all_enemy.forEach(item => {
                if (item.hp.value != 0) {
                    all_die = false;
                }
            });
            if (all_die) {
                // 当前批次敌人全部死亡，检查是否还有敌人队列
                if (this.enemyQueue.length > 0) {
                    // 还有敌人在队列中，清除当前死亡的敌人并生成下一批
                    // 统计击败的精英怪数量
                    const deadEnemies = this.enemy_list.filter(enemy => enemy.hp.value <= 0);
                    deadEnemies.forEach(enemy => {
                        if (enemy.name && (enemy.name.includes('elite') || enemy.name.includes('精') || enemy.name.includes('王'))) {
                            this.defeatedEliteCount++;
                        }
                    });
                    this.enemy_list = this.enemy_list.filter(enemy => enemy.hp.value > 0);
                    this.spawnNextBatch();
                    
                    // 立即设置玩家回合状态，避免被定时器覆盖
                    const setPlayerTurn = () => {
                        this.isPlayerTurn = true;
                        this.bFighting = true;
                        this.show_skill = 'none'; // 重置技能选择状态
                        this.round_info = '【你的回合】';
                        this.setTipsInfo(`击败了当前批次的敌人！下一批敌人出现了！请选择你的技能`);
                        this.playerAttacking = true;
                        
                        // 重置敌人攻击状态
                        this.currentAttackingEnemyIndex = -1;
                        this.attackingEnemyId = null;
                        this.enemyAttacking = false;
                        
                        console.log('下一批敌人状态设置:', {
                            isPlayerTurn: this.isPlayerTurn,
                            bFighting: this.bFighting,
                            show_skill: this.show_skill,
                            enemy_count: this.enemy_list.length
                        });
                    };
                    
                    // 立即设置状态
                    setPlayerTurn();
                    
                    // 延迟再次设置，确保覆盖任何可能的定时器
                    setTimeout(() => {
                        setPlayerTurn();
                        this.$forceUpdate();
                        console.log('延迟确认状态:', {
                            isPlayerTurn: this.isPlayerTurn,
                            bFighting: this.bFighting
                        });
                    }, 100);
                    
                    return;
                } else {
                    // 所有敌人都被击败，战斗真正结束
                    // 统计最后一批死亡敌人中的精英怪
                    const finalDeadEnemies = this.enemy_list.filter(enemy => enemy.hp.value <= 0);
                    finalDeadEnemies.forEach(enemy => {
                        if (enemy.name && (enemy.name.includes('elite') || enemy.name.includes('精') || enemy.name.includes('王'))) {
                            this.defeatedEliteCount++;
                        }
                    });
                    
                    // 随机获得7-12金币
                    let coinReward = Math.floor(Math.random() * 6) + 7; // 7-12金币
                    
                    // 精英怪额外奖励：每只精英怪+15金币
                    const eliteBonus = this.defeatedEliteCount * 15;
                    coinReward += eliteBonus;
                    
                    // 检查聚宝盆护符效果
                    const jubaopen_count = this.hero.amulets.filter(amulet => amulet === 'jubaopen_a').length;
                    if (jubaopen_count > 0) {
                        const boost = jubaopen_count * 0.5; // 每个聚宝盆提升50%
                        coinReward = Math.floor(coinReward * (1 + boost));
                    }
                    
                    this.hero.money += coinReward;
                    
                    // 显示详细的奖励信息
                    let rewardMessage = `你击败了所有敌人！获得了${coinReward}金币！`;
                    
                    this.setTipsInfo(rewardMessage);
                    
                    //TODO:跳转到结算页面
                    this.show_skill = 'fight_over';
                    return;
                }
            }
            
            // 玩家回合结束
            this.isPlayerTurn = false;
            this.show_skill = 'enemy_atk';
            sleep(1000).then(() => {
                this.round_info = '【敌人的回合】';
            });

            sleep(3000).then(() => {
                this.enemyAtk();
            });

        },
        enemyAtk() {
            this.isPlayerTurn = false;
            this.playerAttacking = false; // 玩家回合结束，关闭绿色边框
            this.currentAttackingEnemyIndex = 0;
            this.enemyAttackSequence();
        },
        
        // 敌人逐个攻击
        enemyAttackSequence() {
            const aliveEnemies = this.enemy_list.filter(enemy => enemy.hp.value > 0);
            
            if (this.currentAttackingEnemyIndex >= aliveEnemies.length) {
                // 所有敌人攻击完毕，轮到玩家
                this.currentAttackingEnemyIndex = -1;
                this.attackingEnemyId = null;
                this.isPlayerTurn = true;
                this.bFighting = true;
                this.round_info = '【你的回合】';
                this.tips_info = '请选择你的技能';
                
                // 玩家回合期间持续显示绿色边框
                this.playerAttacking = true;
                return;
            }
            
            const currentEnemy = aliveEnemies[this.currentAttackingEnemyIndex];
            const enemyIndex = this.enemy_list.indexOf(currentEnemy);
            
            // 设置当前攻击的敌人ID（用于UI显示绿色边框）
            this.attackingEnemyId = enemyIndex;
            this.currentAttackingEnemyIndex++;
            
            // 触发当前敌人的攻击动画
            this.enemyAttacking = true;
            setTimeout(() => {
                this.enemyAttacking = false;
                this.attackingEnemyId = null; // 攻击结束后清除ID
            }, 600);
            
            // 计算伤害，考虑防御力减免
            let baseDamage = currentEnemy.atk;
            // 每点防御力减免0.5%伤害
            let damageReduction = this.hero.def * 0.005;
            // 伤害减免不能超过90%
            damageReduction = Math.min(damageReduction, 0.9);
            let hurt = Math.max(1, Math.floor(baseDamage * (1 - damageReduction)));
            
            this.round_info = '【' + currentEnemy.name + '的回合】';
            
            if (this.hero.hp.value - hurt > 0) {
                this.hero.hp.value -= hurt;
                let damageInfo = currentEnemy.name + '攻击了你！';
                damageInfo += `你受到了${hurt}点伤害！`;
                this.setTipsInfo(damageInfo);
                
                // 延迟触发玩家受伤动画
                setTimeout(() => {
                    this.playerTakingDamage = true;
                    setTimeout(() => {
                        this.playerTakingDamage = false;
                    }, 500);
                }, 300);
                
                // 1.5秒后下一个敌人攻击
                setTimeout(() => {
                    this.enemyAttackSequence();
                }, 1500);
            } else {
                this.hero.hp.value = 0;
                this.tips_info = '你受到了致命伤害！';
                // 立即显示死亡页面
                this.showDeathPage = true;
                this.deadHeroData = JSON.parse(JSON.stringify(this.hero)); // 深拷贝英雄数据
                return;
            }
        },
        // Reset game state and return to main menu
        resetGame() {
            // Reset all data properties to their initial state
            Object.assign(this.$data, this.$options.data.call(this));
            this.hero = new hero(); // Ensure hero is also a new instance
            // 重置动画状态
            this.playerAttacking = false;
            this.playerTakingDamage = false;
            this.enemyTakingDamage = false;
            this.enemyAttacking = false;
            // 重置回合状态
            this.isPlayerTurn = true;
            this.currentAttackingEnemyIndex = -1;
            this.attackingEnemyId = null;
            this.toMap(this.mapIdx); // Explicitly reload map data after reset
            this.$emit('childEvent', 100); // Emit event to go to MainStart
        },
        // 处理死亡页面的重新开始
        handleRestartGame(newHero) {
            // 重置所有数据到初始状态
            Object.assign(this.$data, this.$options.data.call(this));
            // 使用传入的新英雄（可能带有选择的装备）
            this.hero = newHero;
            // 重置动画状态
            this.playerAttacking = false;
            this.playerTakingDamage = false;
            this.enemyTakingDamage = false;
            this.enemyAttacking = false;
            // 重置回合状态
            this.isPlayerTurn = true;
            this.currentAttackingEnemyIndex = -1;
            this.attackingEnemyId = null;
            // 隐藏死亡页面
            this.showDeathPage = false;
            this.deadHeroData = null;
            // 重新加载地图
            this.toMap(this.mapIdx);
        },
        // 点击了诗词的备选选项
        clickPoemItem(_item) {
            if (this.taibai_obj.blankList.length <= this.poem_anser_index) {
                return;
            }
            var correct = this.taibai_obj.blankList[this.poem_anser_index];
            // tips: vue修改数组要这样做
            this.$set(this.taibai_obj.anserList, this.poem_anser_index, _item)
            if (_item == correct) {
                this.poem_correct[this.poem_anser_index] = true;
            }
            else {
                this.poem_correct[this.poem_anser_index] = false;
            }
            this.poem_anser_index++;
            if (this.taibai_obj.blankList.length == this.poem_anser_index) {
                var all_right = 0;
                for (let item of this.poem_correct) {
                    if (item) all_right++;
                }
                if (all_right == 0) {
                    this.tips_info = '...你竟然全选错了，技能发动失败';
                    this.bFighting = false; // 禁止选择技能
                    this.round_info = '【敌人的回合】';
                    this.show_skill = 'none'; // 隐藏技能界面
                    
                    sleep(3000).then(() => {
                        this.enemyAtk();
                    });
                } else {
                    let damageMultiplier = all_right / this.poem_correct.length;
                    
                    // 检查是否装备了语文奖状且全对
                    const yuwenjiangzhuangCount = this.hero.amulets.filter(amulet => amulet === 'yuwenjiangzhuang_a').length;
                    if (yuwenjiangzhuangCount > 0 && all_right === this.poem_correct.length) {
                        // 装备了语文奖状且全对，额外增加伤害
                        const bonusMultiplier = yuwenjiangzhuangCount * 0.2;
                        damageMultiplier += bonusMultiplier;
                        this.tips_info = `完美！太白剑法填空全对，伤害提升${Math.round(bonusMultiplier * 100)}%！`;
                    }
                    
                    // 检查字来宝效果：每装备一个伤害增加20%
                    const zilaibaoCount = this.hero.amulets.filter(amulet => amulet === 'zilaibao_a').length;
                    if (zilaibaoCount > 0) {
                        const zilaibaoBonus = zilaibaoCount * 0.2;
                        damageMultiplier += zilaibaoBonus;
                    }
                    
                    // 检查字去宝效果：每装备一个伤害下降5%
                    const ziqubaoCount = this.hero.amulets.filter(amulet => amulet === 'ziqubao_a').length;
                    if (ziqubaoCount > 0) {
                        const ziqubaoReduction = ziqubaoCount * 0.05;
                        damageMultiplier = Math.max(0.1, damageMultiplier - ziqubaoReduction); // 最低保持10%伤害
                    }
                    
                    // 检查工部诗集效果：每装备一个伤害增加20%
                    const gongbushijiCount = this.hero.amulets.filter(amulet => amulet === 'gongbushiji_a').length;
                    if (gongbushijiCount > 0) {
                        const gongbushijiBonus = gongbushijiCount * 0.2;
                        damageMultiplier += gongbushijiBonus;
                    }
                    
                    // 时间炸弹效果：剩余时间转化为伤害
                    const shijianzhaCount = this.hero.amulets.filter(amulet => amulet === 'shijianzha_a').length;
                    if (shijianzhaCount > 0) {
                        const remainingTimeBonus = (this.percentage / 100) * shijianzhaCount * 0.01; // 每秒剩余时间增加1%伤害
                        damageMultiplier += remainingTimeBonus;
                    }
                    
                    this.useSkill(this.select_skill, damageMultiplier);
                }
                clearInterval(this.intervalHandler);
                this.percentage = 100;
            }
        },
        // 点击了九章算术的备选选项
        clickJiuzhangNumItem(_item) {
            if (this.jiuzhang_obj.leftList.length <= this.jiuzhang_anser_index) {
                return;
            }
            this.$set(this.jiuzhang_obj.anserList, this.jiuzhang_anser_index, _item);
            this.jiuzhang_anser_index++;
            if (this.jiuzhang_obj.anserList.length == this.jiuzhang_anser_index) {
                var anser_tal = 0;
                for (let item of this.jiuzhang_obj.anserList) {
                    anser_tal += parseInt(item);
                }
                
                let hasRepeat = false;
                let numSet = new Set(this.jiuzhang_obj.anserList);
                hasRepeat = numSet.size !== this.jiuzhang_obj.anserList.length;
                
                if (anser_tal == this.jiuzhang_obj.total) {
                    const duomiantiCount = this.hero.amulets.filter(amulet => amulet === 'datongin_a').length;
                    const datonginCount = this.hero.amulets.filter(amulet => amulet === 'datongin_b').length;
                    
                    // 检查是否所有数字完全相同
                    const allSame = this.jiuzhang_obj.anserList.every(num => num === this.jiuzhang_obj.anserList[0]);
                    
                    if (duomiantiCount > 0 && !hasRepeat) {
                        // 装备了多面体，数字互不相同时的效果
                        const totalBonus = 1.0 + (duomiantiCount * 0.2);
                        this.useSkill(this.select_skill, totalBonus);
                        this.tips_info = `完美！所有数字互不相同，伤害提升${Math.round((totalBonus - 1) * 100)}%！`;
                    } 
                    if (datonginCount > 0 && allSame) {
                        // 装备了大同印，数字完全相同时的效果
                        const totalBonus = 1.0 + (datonginCount * 0.2);
                        this.useSkill(this.select_skill, totalBonus);
                        this.tips_info = `完美！所有数字完全相同，伤害提升${Math.round((totalBonus - 1) * 100)}%！`;
                    } else {
                        this.useSkill(this.select_skill, 1);
                    }
                } else {
                    this.tips_info = '你这得数是' + anser_tal + '，技能发动失败';
                    this.bFighting = false; // 禁止选择技能
                    this.round_info = '【敌人的回合】';
                    this.show_skill = 'none'; // 隐藏技能界面
                    
                    sleep(3000).then(() => {
                        this.enemyAtk();
                    });
                }
                clearInterval(this.intervalHandler);
                this.percentage = 100;
            }
        },
        // 选中了七益拳的颜色选项
        clickQiyiColorItem(color) {
            const totalSlots = this.qiyiquan_obj.answerList.length;
            
            if (this.qiyiquan_obj.showColors || this.qiyiquan_obj.currentAnswerIndex >= totalSlots) {
                return; // 如果还在显示颜色阶段或已完成，不处理点击
            }
            
            // 填入选择的颜色
            this.$set(this.qiyiquan_obj.answerList, this.qiyiquan_obj.currentAnswerIndex, color);
            this.qiyiquan_obj.currentAnswerIndex++;
            
            // 检查是否已完成所有填空
            if (this.qiyiquan_obj.currentAnswerIndex >= totalSlots) {
                this.checkQiyiAnswers();
            }
        },
        
        // 检查七益拳答案
        checkQiyiAnswers() {
            let correctCount = 0;
            const totalSlots = this.qiyiquan_obj.answerList.length;
            
            for (let i = 0; i < totalSlots; i++) {
                if (this.qiyiquan_obj.answerList[i] === this.qiyiquan_obj.colorSequence[i]) {
                    correctCount++;
                }
            }
            
            if (correctCount === 0) {
                this.tips_info = '一个都没记对，技能发动失败！';
                this.bFighting = false;
                this.round_info = '【敌人的回合】';
                this.show_skill = 'none';
                
                sleep(3000).then(() => {
                    this.enemyAtk();
                });
            } else {
                // 计算七色花的伤害加成
                const qisehuaCount = this.hero.amulets.filter(amulet => amulet === 'qisehua_a').length;
                let damageMultiplier = correctCount / totalSlots;
                
                // 每个七色花增加25%伤害
                if (qisehuaCount > 0) {
                    damageMultiplier *= (1 + qisehuaCount * 0.25);
                }
                
                // 时间炸弹效果：剩余时间转化为伤害
                const shijianzhaCount = this.hero.amulets.filter(amulet => amulet === 'shijianzha_a').length;
                if (shijianzhaCount > 0) {
                    const remainingTimeBonus = (this.percentage / 100) * shijianzhaCount * 0.01; // 每秒剩余时间增加1%伤害
                    damageMultiplier += remainingTimeBonus;
                }
                
                this.tips_info = `记对了${correctCount}个颜色！`;
                this.useSkill(this.select_skill, damageMultiplier);
            }
            
            clearInterval(this.intervalHandler);
            this.percentage = 100;
        },
        // 选中了某个盲盒
        clickGiftItem(item) {
            let info = '';
            switch(item.type) {
                case 1: // 恢复盲盒 - 血量全满
                    if (this.hero.hp.value < this.hero.hp.max) {
                        const actualHpRecover = this.hero.hp.max - this.hero.hp.value;
                        this.hero.hp.value = this.hero.hp.max;
                        info = `恢复了${actualHpRecover}点生命值，血量已满！`;
                    } else {
                        info = '生命值已满';
                    }
                    
                    // 显示提示信息
                    Message({
                        message: info,
                        type: 'success',
                        duration: 2000,
                        showClose: true
                    });
                    break;

                case 2: // 属性盲盒 - 随机增加20%的任意属性
                    const rand = Math.floor(Math.random() * 3);
                    switch(rand) {
                        case 0: // 增加20%最大生命值
                            const hpIncrease = Math.floor(this.hero.hp.max * 0.20);
                            this.hero.hp.max += hpIncrease;
                            info = `最大生命值提升+${hpIncrease}点`;
                            break;
                        case 1: // 增加20%攻击力
                            const atkIncrease = Math.floor(this.hero.base_atk * 0.20);
                            this.hero.base_atk += atkIncrease;
                            this.hero.atk += atkIncrease;
                            info = `攻击力提升+${atkIncrease}点`;
                            break;
                        case 2: // 增加20%防御力
                            const defIncrease = Math.floor(this.hero.def * 0.20);
                            this.hero.def += defIncrease;
                            info = `防御力提升+${defIncrease}点`;
                            break;
                    }
                    Message({
                        message: info,
                        type: 'success',
                        duration: 2000,
                        showClose: true
                    });
                    break;

                case 3: // 技能盲盒 - 随机增加一个技能
                    const availableSkills = ['taibai_j', 'jiuzhang_z', 'qiyi_q'];
                    const randomSkill = availableSkills[Math.floor(Math.random() * availableSkills.length)];
                    const skillGift = { name: randomSkill, type: 'skill' };
                    const skillName = this.hero.gainAThing(skillGift);
                    if (skillName) {
                        Message({
                            message: `获得了技能：${skillName}！`,
                            type: 'success',
                            duration: 2000,
                            showClose: true
                        });
                    }
                    break;

                case 4: // 护符盲盒 - 随机获得一个护符
                    const availableAmulets = ['wulingzhu_a', 'shulaibao_a', 'shuqubao_a', 'datongin_a', 'datongin_b', 'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a'];
                    
                    const randomAmulet = availableAmulets[Math.floor(Math.random() * availableAmulets.length)];
                    const amuletGift = { name: randomAmulet, type: 'equip' };
                    const amuletName = this.hero.gainAThing(amuletGift);
                    if (amuletName) {
                        Message({
                            message: `获得了护符：${amuletName}！`,
                            type: 'success',
                            duration: 2000,
                            showClose: true
                        });
                    }
                    break;

                case 5: // 奇遇宝箱 - 随机获得护符
                    console.log('处理奇遇宝箱...');
                    const adventureItems = [
                        // 护符
                        'wulingzhu_a', 'shulaibao_a', 'shuqubao_a', 'datongin_a', 'datongin_b',
                        'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a',
                        'shijianzha_a', 'chuancizhen_a', 'jubaopen_a'
                    ];
                    
                    const randomItem = adventureItems[Math.floor(Math.random() * adventureItems.length)];
                    console.log('随机选择的物品:', randomItem);
                    
                    // 检查装备是否存在
                    const equipData = this.getEquipByName(randomItem);
                    console.log('装备数据:', equipData);
                    
                    const adventureGift = { name: randomItem, type: 'equip' };
                    const itemName = this.hero.gainAThing(adventureGift);
                    console.log('获得的物品名称:', itemName);
                    console.log('当前背包:', this.hero.props);
                    
                    if (itemName && itemName !== '无') {
                        const itemType = equipData.type;
                        let typeText = '';
                        switch(itemType) {
                            case 'amulet': typeText = '护符'; break;
                            default: typeText = '护符'; break;
                        }
                        console.log('显示消息:', `奇遇获得了${typeText}：${itemName}！已添加到背包中。`);
                        Message({
                            message: `奇遇获得了${typeText}：${itemName}！已添加到背包中。`,
                            type: 'warning',
                            duration: 3000,
                            showClose: true
                        });
                    } else {
                        console.error('获取物品失败:', randomItem, '返回的物品名称:', itemName);
                        console.error('装备数据:', equipData);
                        Message({
                            message: '奇遇宝箱打开失败！',
                            type: 'error',
                            duration: 2000,
                            showClose: true
                        });
                    }
                    break;
            }
            
            // 如果是奇遇宝箱，不自动跳转，让玩家通过继续冒险按钮控制
            if (item.type === 5) {
                return;
            }
            
            // 处理完礼物后跳转到下一个场景
            const nextId = this.mapData[this.dataIdx].to_id;
            
            if (nextId === undefined || nextId === null) {
                console.error('跳转ID为空!');
                return;
            }
            
            const nextIdStr = String(nextId);
            if (!this.mapData[nextIdStr]) {
                console.error('无效的跳转目标:', nextId);
                return;
            }
            
            this.dataIdx = nextIdStr;
            this.show_div = this.mapData[this.dataIdx].type;
            
            // 如果跳转到的是战斗场景，需要初始化敌人
            if (this.show_div === 'fight') {
                // 初始化敌人队列
                this.enemyQueue = [];
                for (let item1 of this.mapData[this.dataIdx].enemyList || []) {
                    this.enemyQueue.push(this.getEnemyByName(item1));
                }
                
                // 显示第一批敌人（最多3个）
                this.enemy_list = [];
                this.spawnNextBatch();
                
                this.bFighting = true;
                this.defeatedEliteCount = 0; // 重置精英怪计数器
                this.show_skill = 'none';
                this.tips_info = '开始战斗！（请点击上方的技能）';
                this.round_info = '【你的回合】';
                
                // 玩家回合期间持续显示绿色边框
                this.playerAttacking = true;
            }
        },
        handleChange(val) {
            console.log('当前展开的面板:', val);
        },
        // 进度条相关
        setHpProgress(_data) {
            if (!_data || typeof _data !== 'object' || _data.value === undefined || _data.max === undefined) {
                return 0;
            }
            if (_data.value > _data.max) {
                return 100
            } else {
                return parseInt((_data.value / _data.max).toFixed(1) * 100)
            }
        },
        setHpText(row) {
            return () => { 
                if (!row || typeof row !== 'object' || row.value === undefined || row.max === undefined) {
                    return '0/0';
                }
                return row.value + '/' + row.max 
            }
        },
        // 根据血量百分比设置颜色
        getHpColor(hpData) {
            if (!hpData || typeof hpData !== 'object' || hpData.value === undefined || hpData.max === undefined) {
                return '#409eff'; // 默认蓝色
            }
            const percentage = (hpData.value / hpData.max) * 100;
            if (percentage <= 15) {
                return '#f56c6c'; // 红色
            } else if (percentage <= 50) {
                return '#e6a23c'; // 橙色
            } else {
                return '#67c23a'; // 绿色
            }
        },
        // 进度条减少
        val_reduce(_time) {
            if (this.percentage > 0) {
                this.percentage -= _time;
            }
            else {
                switch (this.show_skill) {
                    case 'taibai_j':
                        this.poem_anser_index = this.taibai_obj.blankList.length;// 技能结束
                        this.tips_info = '时间到，技能发动失败';
                        this.round_info = '【敌人的回合】';
                        sleep(3000).then(() => {
                            this.enemyAtk();
                        });
                        break;
                    case 'jiuzhang_z':
                        this.jiuzhang_anser_index = this.jiuzhang_obj.anserList.length;// 技能结束
                        this.tips_info = '时间到，技能发动失败';
                        this.round_info = '【敌人的回合】';
                        sleep(3000).then(() => {
                            this.enemyAtk();
                        });
                        break;
                    case 'qiyi_q':
                        // 时间到了，检查当前已填入的答案
                        if (this.qiyiquan_obj.currentAnswerIndex > 0) {
                            this.checkQiyiAnswers();
                        } else {
                            this.tips_info = '时间到，技能发动失败';
                            this.bFighting = false;
                            this.round_info = '【敌人的回合】';
                            this.show_skill = 'none';
                            sleep(3000).then(() => {
                                this.enemyAtk();
                            });
                        }
                        break;
                    case 'fight_over':
                        // 跳转到下个场景
                        const nextSceneId = String(this.mapData[this.dataIdx].to_id);
                        this.dataIdx = nextSceneId;
                        this.show_div = this.mapData[this.dataIdx].type;
                        break;
                }
                clearInterval(this.intervalHandler);
                this.percentage = 100;
            }

        },
        // 计时器
        countdown(_time) {
            this.intervalHandler = setInterval(this.val_reduce, 1000, _time);

        },
        // 生成下一批敌人
        spawnNextBatch() {
            const batchSize = Math.min(this.currentBatchSize, this.enemyQueue.length);
            for (let i = 0; i < batchSize; i++) {
                if (this.enemyQueue.length > 0) {
                    const enemy = this.enemyQueue.shift(); // 从队列中取出敌人
                    this.enemy_list.push(enemy);
                }
            }
            
            if (this.enemy_list.length > 0) {
                const remainingEnemies = this.enemyQueue.length;
                if (remainingEnemies > 0) {
                    this.tips_info += `（还有${remainingEnemies}个敌人在后方等待）`;
                }
            }
        },
        // 战斗结束处理
        handleFightOver() {
            this.bFighting = false;
            this.show_skill = 'fight_over';
            this.countdown(100);
        },
        // 尝试装备
        tryEquip(propName) {
            const equip = this.getEquipByName(propName);
            if (equip.type === 'weapon') {
                // 保存当前武器到背包
                if (this.hero.weapon !== 'none') {
                    this.hero.props.push(this.hero.weapon);
                }
                // 装备新武器
                this.hero.weapon = propName;
                // 从背包中移除
                const index = this.hero.props.indexOf(propName);
                if (index > -1) {
                    this.hero.props.splice(index, 1);
                }
                // 更新攻击力
                this.hero.setATK(this.hero.base_atk + equip.hurt);
                
                Message({
                    message: `装备了${equip.name}`,
                    type: 'success'
                });
            }
        },


        selectTreasure(item) {
            if (item.gift) {
                const itemName = this.hero.gainAThing(item.gift);
                if (itemName) {
                    Message({
                        message: `获得了${itemName}！`,
                        type: 'success'
                    });
                }
            }
            
            if (item.to_id !== undefined) {
                this.dataIdx = String(item.to_id);
                this.show_div = this.mapData[this.dataIdx].type;
            }
        },
        // 打开奇遇宝箱
        openAdventureTreasure() {
            if (this.adventureTreasureOpened) {
                return;
            }
            
            // 标记宝箱已开启
            this.adventureTreasureOpened = true;
            
            // 获取第一个礼物（奇遇宝箱）
            if (this.mapData[this.dataIdx].giftList && this.mapData[this.dataIdx].giftList.length > 0) {
                const gift = this.mapData[this.dataIdx].giftList[0];
                console.log('手动打开奇遇宝箱:', gift);
                this.clickGiftItem(gift);
            }
        },
        // Shop methods
        buyItem(itemKey) {
            const item = this.getEquipByName(itemKey);
            if (!item || item.price === undefined) { // Check for undefined price
                Message.error('商品信息错误！');
                return;
            }
            if (this.hero.money >= item.price) {
                this.hero.money -= item.price;
                // Determine if it's an equip or item based on its type in equip.js
                const giftType = (item.type === 'weapon' || item.type === 'prop' || item.type === 'protective' || item.type === 'poem' || item.type === 'amulet') ? 'equip' : 'item';
                this.hero.gainAThing({ name: itemKey, type: giftType });
                
                // 如果购买的是护符，从商店物品列表中移除
                if (item.type === 'amulet') {
                    const shopItems = this.mapData[this.dataIdx].shopItems;
                    const itemIndex = shopItems.indexOf(itemKey);
                    if (itemIndex > -1) {
                        shopItems.splice(itemIndex, 1);
                    }
                }
                
                Message({
                    message: `成功购买 ${item.name}！`,
                    type: 'success',
                    showClose: true
                });
            } else {
                Message.warning('金钱不足！');
            }
        },
        sellItem(itemKey, type) {
            const item = this.getEquipByName(itemKey);
            if (!item || item.price === undefined) { // Check for undefined price
                Message.error('商品信息错误！');
                return;
            }
            const sellPrice = Math.floor(item.price * 0.8); // Sell for 80% price
            
            let index = -1;
            if (type === 'prop') {
                index = this.hero.props.indexOf(itemKey);
                if (this.hero.weapon === itemKey) { // Cannot sell equipped weapon
                    Message.warning('不能出售当前装备的武器！');
                    return;
                }
                if (this.hero.protective === itemKey) { // Cannot sell equipped protective gear
                    Message.warning('不能出售当前装备的护具！');
                    return;
                }
            } else if (type === 'item') {
                index = this.hero.items.indexOf(itemKey);
            }

            if (index > -1) {
                if (type === 'prop') {
                    this.hero.props.splice(index, 1);
                } else if (type === 'item') {
                    this.hero.items.splice(index, 1);
                }
                this.hero.money += sellPrice;
                Message({
                    message: `成功出售 ${item.name}，获得 ${sellPrice} 金钱！`,
                    type: 'success',
                    showClose: true
                });
            } else {
                Message.error('出售失败，物品不在背包中！');
            }
        },
        
        // 背包相关方法
        equipWeapon(itemKey) {
            const item = this.getEquipByName(itemKey);
            if (!item || item.type !== 'weapon') {
                Message.error('无法装备该物品！');
                return;
            }
            
            // 如果当前有武器，放回背包并移除其属性
            if (this.hero.weapon !== 'none') {
                const oldWeapon = this.getEquipByName(this.hero.weapon);
                // 移除旧武器的吸血属性
                if (oldWeapon.lifesteal) {
                    this.hero.setLifesteal(this.hero.lifesteal - oldWeapon.lifesteal);
                }
                this.hero.props.push(this.hero.weapon);
            }
            
            // 从背包中移除新武器
            const index = this.hero.props.indexOf(itemKey);
            if (index > -1) {
                this.hero.props.splice(index, 1);
            }
            
            // 装备新武器
            this.hero.weapon = itemKey;
            this.hero.setATK(this.hero.base_atk + item.hurt);
            
            // 处理武器的吸血属性
            if (item.lifesteal) {
                this.hero.setLifesteal(this.hero.lifesteal + item.lifesteal);
            }
            
            Message({
                    message: `成功装备 ${item.name}！`,
                    type: 'success',
                    showClose: true
                });
        },
        
        equipProtective(itemKey) {
            const item = this.getEquipByName(itemKey);
            if (!item || item.type !== 'protective') {
                Message.error('无法装备该物品！');
                return;
            }
            
            // 如果当前有护具，卸下并放回背包
            if (this.hero.protective !== 'none') {
                const oldItem = this.getEquipByName(this.hero.protective);
                // 移除旧护具的属性加成
                if (oldItem.def) {
                    this.hero.def -= oldItem.def;
                }
                if (oldItem.hp_bonus) {
                    this.hero.hp.max -= oldItem.hp_bonus;
                    if (this.hero.hp.value > this.hero.hp.max) {
                        this.hero.hp.value = this.hero.hp.max;
                    }
                }
                this.hero.props.push(this.hero.protective);
            }
            
            // 从背包中移除新护具
            const index = this.hero.props.indexOf(itemKey);
            if (index > -1) {
                this.hero.props.splice(index, 1);
            }
            
            // 装备新护具并应用属性加成
            this.hero.protective = itemKey;
            if (item.def) {
                this.hero.def += item.def;
            }
            if (item.hp_bonus) {
                this.hero.hp.max += item.hp_bonus;
            }
            
            Message({
                message: `成功装备 ${item.name}！`,
                type: 'success',
                showClose: true
            });
        },
        
        getEquipTypeText(type) {
            const typeMap = {
                'weapon': '武器',
                'protective': '护具',
                'amulet': '护符',
                'prop': '道具',
                'poem': '诗词'
            };
            return typeMap[type] || '未知';
        },
        
        // 获取排序后的道具列表
        getSortedProps() {
            return [...this.hero.props].sort((a, b) => {
                const itemA = this.getEquipByName(a);
                const itemB = this.getEquipByName(b);
                
                // 按类型排序：武器 > 护具 > 护符 > 其他
                const typeOrder = { 'weapon': 1, 'protective': 2, 'amulet': 3, 'prop': 4, 'poem': 5 };
                const orderA = typeOrder[itemA.type] || 5;
                const orderB = typeOrder[itemB.type] || 5;
                
                if (orderA !== orderB) {
                    return orderA - orderB;
                }
                
                // 同类型按等级排序
                if (itemA.level !== itemB.level) {
                    return (itemB.level || 0) - (itemA.level || 0);
                }
                
                // 最后按名称排序
                return itemA.name.localeCompare(itemB.name);
            });
        },
        

        
        // 获取装备的详细信息
        getEquipDetailInfo(itemKey) {
            const item = this.getEquipByName(itemKey);
            let info = [];
            
            if (item.hurt) {
                info.push(`攻击力: +${item.hurt}`);
            }
            if (item.def) {
                info.push(`防御力: +${item.def}`);
            }
            if (item.hp_bonus) {
                info.push(`生命值: +${item.hp_bonus}`);
            }
            if (item.lifesteal) {
                info.push(`吸血: ${(item.lifesteal * 100).toFixed(1)}%`);
            }
            if (item.atk_bonus) {
                info.push(`攻击力: +${item.atk_bonus}`);
            }
            if (item.def_bonus) {
                info.push(`防御力: +${item.def_bonus}`);
            }
            if (item.lifesteal_bonus) {
                info.push(`吸血: +${(item.lifesteal_bonus * 100).toFixed(1)}%`);
            }
            if (item.crit_bonus) {
                info.push(`暴击率: +${(item.crit_bonus * 100).toFixed(1)}%`);
            }
            if (item.level !== undefined) {
                info.push(`等级: ${item.level}`);
            }
            if (item.price) {
                info.push(`价值: ${item.price}金`);
            }
            
            return info.join(' | ');
        },
        
        // 卸下武器
        unequipWeapon() {
            if (this.hero.weapon === 'none') {
                Message.warning('当前没有装备武器！');
                return;
            }
            
            // 获取当前武器信息
            const currentWeapon = this.getEquipByName(this.hero.weapon);
            const weaponName = this.getEquipName(this.hero.weapon);
            
            // 将武器放回背包
            this.hero.props.push(this.hero.weapon);
            
            // 卸下武器，恢复基础攻击力
            this.hero.weapon = 'none';
            this.hero.setATK(this.hero.base_atk);
            
            // 移除武器的吸血属性
            if (currentWeapon.lifesteal) {
                this.hero.setLifesteal(this.hero.lifesteal - currentWeapon.lifesteal);
            }
            
            Message({
                    message: `成功卸下 ${weaponName}！`,
                    type: 'success',
                    showClose: true
                });
        },
        
        // 卸下护具
        unequipProtective() {
            if (this.hero.protective === 'none') {
                Message.warning('当前没有装备护具！');
                return;
            }
            
            const item = this.getEquipByName(this.hero.protective);
            const protectiveName = this.getEquipName(this.hero.protective);
            
            // 移除护具的属性加成
            if (item.def) {
                this.hero.def -= item.def;
            }
            if (item.hp_bonus) {
                this.hero.hp.max -= item.hp_bonus;
                if (this.hero.hp.value > this.hero.hp.max) {
                    this.hero.hp.value = this.hero.hp.max;
                }
            }
            
            // 将护具放回背包
            this.hero.props.push(this.hero.protective);
            
            // 卸下护具
            this.hero.protective = 'none';
            
            Message({
                    message: `成功卸下 ${protectiveName}！`,
                    type: 'success',
                    showClose: true
                });
        },
        
        // 装备护符
        equipAmulet(amuletKey) {
            // 允许特定护符重复装备
            const canRepeatEquip = ['wulingzhu_a', 'shulaibao_a', 'shuqubao_a', 'datongin_a', 'datongin_b', 'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a'];
            
            if (this.hero.amulets.includes(amuletKey) && !canRepeatEquip.includes(amuletKey)) {
                Message.warning('该护符已经装备！');
                return;
            }
            
            if (this.hero.amulets.length >= this.hero.max_amulets) {
                Message.warning('护符栏已满！');
                return;
            }
            
            // 从背包中移除护符
            const index = this.hero.props.indexOf(amuletKey);
            if (index > -1) {
                this.hero.props.splice(index, 1);
            }
            
            // 装备护符
            const amulet = this.getEquipByName(amuletKey);
            this.hero.amulets.push(amuletKey);
            
            // 应用护符属性加成
            if (amulet.atk_bonus) {
                this.hero.setATK(this.hero.atk + amulet.atk_bonus);
            }
            if (amulet.def_bonus) {
                this.hero.setDEF(this.hero.def + amulet.def_bonus);
            }
            if (amulet.hp_bonus) {
                this.hero.setMaxHP(this.hero.hp.max + amulet.hp_bonus);
            }
            if (amulet.lifesteal_bonus) {
                this.hero.setLifesteal(this.hero.lifesteal + amulet.lifesteal_bonus);
            }
            if (amulet.crit_bonus) {
                this.hero.baoji_rate += amulet.crit_bonus;
            }
            
            // 如果装备的是影响九章掌法的护符，重新初始化九章掌法
            if (amuletKey === 'wulingzhu_a' || amuletKey === 'shulaibao_a' || amuletKey === 'shuqubao_a') {
                this.initJiuzhangInfo();
            }
            
            // 如果装备的是影响太白剑法的护符，重新初始化太白剑法
            if (amuletKey === 'ziqubao_a') {
                this.initTaibaiInfo();
            }
            
            // 如果装备的是影响七益拳的护符，重新初始化七益拳
            if (amuletKey === 'qisehua_a') {
                this.initQiyiInfo();
            }
            
            // 如果装备的是工部诗集，添加杜甫诗作到可选诗中
            if (amuletKey === 'gongbushiji_a') {
                if (!this.hero.poem_arr.includes('dufu')) {
                    this.hero.poem_arr.push('dufu');
                }
            }
            
            const amuletName = this.getEquipName(amuletKey);
            Message({
                    message: `成功装备 ${amuletName}！`,
                    type: 'success',
                    showClose: true
                });
        },
        
        // 移除护符
        removeAmulet(amuletKey) {
            const success = this.hero.removeAmulet(amuletKey);
            if (success) {
                // 如果卸下的是影响九章掌法的护符，重新初始化九章掌法
                if (amuletKey === 'wulingzhu_a' || amuletKey === 'shulaibao_a' || amuletKey === 'shuqubao_a') {
                    this.initJiuzhangInfo();
                }
                
                // 如果卸下的是影响太白剑法的护符，重新初始化太白剑法
                if (amuletKey === 'ziqubao_a') {
                    this.initTaibaiInfo();
                }
                
                // 如果卸下的是影响七益拳的护符，重新初始化七益拳
                if (amuletKey === 'qisehua_a') {
                    this.initQiyiInfo();
                }
                
                // 如果卸下的是工部诗集，检查是否还有其他工部诗集，如果没有则移除dufu
                if (amuletKey === 'gongbushiji_a') {
                    const remainingGongbu = this.hero.amulets.filter(a => a === 'gongbushiji_a').length;
                    if (remainingGongbu === 0) {
                        const dufuIndex = this.hero.poem_arr.indexOf('dufu');
                        if (dufuIndex > -1) {
                            this.hero.poem_arr.splice(dufuIndex, 1);
                        }
                    }
                }
                
                const amuletName = this.getEquipName(amuletKey);
                Message({
                    message: `成功卸下 ${amuletName}！`,
                    type: 'success',
                    showClose: true
                });
            } else {
                Message.error('移除护符失败！');
            }
        },
        // 显示道具详情
        showItemDetail(itemKey) {
            this.selectedItem = this.getEquipByName(itemKey);
            this.itemDetailDialog = true;
        },
        // 关闭道具详情弹窗
        closeItemDetail() {
            this.itemDetailDialog = false;
            this.selectedItem = null;
        },
        // 获取装备类型的中文名称
        getEquipTypeText(type) {
            const typeMap = {
                'weapon': '武器',
                'protective': '防具',
                'amulet': '护符'
            };
            return typeMap[type] || '未知';
        }
    },
    // 设计思路：map数据是一个n套数据的数组，每一套数据都包含段落、普通选择和方向选择（可能还有其它，用type区分即可，反正这些都是一个列表，只是可能列表的容器不同）。
    // 这一个数组的数组代表了这个map里的所有数据
    // 这个数组里的一个对象代表了一个页面里的所有数据
}
</script>
<template>
  <div>
    <transition name="fade">
        <div>
            <!--战斗模块-->
            <div class="fighting_main" v-show="show_div == 'fight'">
                <!--战斗信息模块-->
                <el-row class="fight_info" :gutter="5">
                    <el-col :span="12">
                        <el-card class="box-card fight_info" :class="{ 'attack-animation': playerAttacking, 'damage-animation': playerTakingDamage }">
                            <div slot="header" class="clearfix">
                                <span>你的信息</span>
                            </div>
                            <div class="player-info-section">
                                <div class="info-row">
                                    <i class="el-icon-s-data" style="color: #f56c6c; margin-right: 5px;"></i>
                                    <el-progress :percentage="setHpProgress(hero.hp)" :format="setHpText(hero.hp)" :color="getHpColor(hero.hp)">
                                    </el-progress>
                                </div>
                                <div class="info-row">
                                    <i class="el-icon-knife-fork" style="color: #409eff; margin-right: 5px;"></i>
                                    <el-button type="text" @click="showItemDetail(hero.weapon)" :disabled="!hero.weapon">{{ getEquipName(hero.weapon) }}</el-button>
                                </div>
                                <div class="info-row">
                                    <i class="el-icon-medal" style="color: #67c23a; margin-right: 5px;"></i>
                                    <el-button type="text" @click="showItemDetail(hero.protective)" :disabled="!hero.protective">{{ getEquipName(hero.protective) }}</el-button>
                                </div>
                                <div class="info-row">
                                    <i class="el-icon-star-on" style="color: #e6a23c; margin-right: 5px;"></i>
                                    <div class="amulets-display">
                                        <span v-if="!hero.amulets || hero.amulets.length === 0" class="no-equipment">无</span>
                                        <div v-else class="amulet-tags">
                                            <el-tag v-for="(amulet, index) in hero.amulets" :key="'amulet-' + index" 
                                                    size="mini" type="warning" style="margin-right: 3px; cursor: pointer;"
                                                    @click="showItemDetail(amulet)">
                                                {{ getEquipName(amulet).charAt(0) }}
                                            </el-tag>
                                        </div>
                                    </div>
                                </div>
                            </div>
                       </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="box-card fight_info" :class="{ 'damage-animation': enemyTakingDamage, 'hit-animation': enemyTakingDamage, 'enemy-attack-animation': enemyAttacking }">
                            <div slot="header" class="clearfix">
                                <span>敌人信息{{ getEnemyBatchInfo() }}</span>
                            </div>
                            <div class="enemy-info-section">
                                <div v-for="(item, index) in enemy_list" :key="'enemy-' + index" v-if="item && item.name && item.hp" class="enemy-card" :class="{ 'enemy-attack-animation': attackingEnemyId === index }">
                                    <div class="enemy-name-row">
                                        <span class="enemy-name">{{ item.name }}</span>
                                        <span class="enemy-attack">攻击力: {{ item.atk || '未知' }}</span>
                                    </div>
                                    <div class="enemy-hp-row">
                                        <i class="el-icon-s-data" style="color: #f56c6c; margin-right: 5px;"></i>
                                        <el-progress :percentage="setHpProgress(item.hp)" :format="setHpText(item.hp)" :color="getHpColor(item.hp)">
                                        </el-progress>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!--技能选择模块-->
                <el-row class="skill_list">
                    <el-badge v-for="item in hero.skills" style="margin: 10px;" :value="item.level" class="item"
                        type="primary">
                        <el-button size="small" @click="selectSkill(item)" plain :disabled="!isPlayerTurn || !bFighting" :class="{ 'skill-disabled': !isPlayerTurn }">
                            {{ item.name }}
                        </el-button>
                    </el-badge>
                </el-row>

                <!--战斗提示模块-->
                <el-divider><i class="el-icon-warning-outline"></i></el-divider>
                <p class="fight_tips_info" style="text-align: center;line-height: 15px;">{{ round_info }}</p>
                <p class="fight_tips_info" style="text-align: center;line-height: 15px;">{{ tips_info }}</p>
                <!--技能操作模块-->
                <el-divider><i class="el-icon-star-on"></i></el-divider>
                <!--技能（太白剑诀）模块-->
                <div class="skill_taibai_j" v-show="show_skill == 'taibai_j'">
                    <el-row class="skill_info">
                        <el-col :span="24">
                            <el-card class="box-card">
                                <div slot="header" style="text-align:center;" class="clearfix">
                                    <span v-for="(item, index) in taibai_obj.anserList">
                                        {{ taibai_obj.replaceStrArr[index] }}<span
                                            :style="{ color: (poem_correct[index] == true ? '#000000' : '#F56C6C') }">{{
                                                    item
                                            }}</span>
                                    </span>
                                    {{ taibai_obj.replaceStrArr[taibai_obj.replaceStrArr.length - 1] }}
                                </div>

                                <el-button style="margin: 10px;" v-for="(item, index) in taibai_obj.blurList"
                                    type="primary" @click="clickPoemItem(item)" plain>
                                    {{ item }}
                                </el-button>
                                <el-col :span="12" :offset="6">
                                    <el-progress type="circle" :percentage="percentage" :format="() => ''">
                                    </el-progress>
                                </el-col>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
                <!--技能（九章掌法）模块-->
                <div class="skill_jiuzhang_z" v-show="show_skill == 'jiuzhang_z'">
                    <el-row class="skill_info">
                        <el-col :span="24">
                            <el-card class="box-card">
                                <div slot="header" style="text-align:center;" class="clearfix">
                                    <span v-for="(item, index) in jiuzhang_obj.anserList">
                                        <span
                                            :style="{ color: (jiuzhang_correct[index] == true ? '#000000' : '#F56C6C') }">
                                            {{ item }}
                                        </span><span v-show="index != jiuzhang_obj.anserList.length - 1">+</span>
                                    </span>
                                    = {{ jiuzhang_obj.total }}
                                </div>

                                <el-button style="margin: 10px;" v-for="(item, index) in getJiuzhangNumbers()"
                                    type="primary" @click="clickJiuzhangNumItem(item)" plain>
                                    {{ item }}
                                </el-button>

                                <el-col :span="12" :offset="6">
                                    <el-progress type="circle" :percentage="percentage" :format="() => ''">
                                    </el-progress>
                                </el-col>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
                <!--技能（七益拳）模块-->
                <div class="skill_qiyi_q" v-show="show_skill == 'qiyi_q'">
                    <el-row class="skill_info">
                        <el-col :span="24">
                            <el-card class="box-card">
                                <!-- 显示颜色方块阶段 -->
                                <div v-if="qiyiquan_obj.showColors" style="text-align:center;" class="clearfix">
                                    <div slot="header" style="margin-bottom: 20px;">
                                        <span style="font-size: 16px; font-weight: bold;">请记住下面方块的颜色：</span>
                                    </div>
                                    <div style="display: flex; justify-content: center; gap: 10px; margin: 20px 0;">
                                        <div 
                                            v-for="(color, index) in qiyiquan_obj.colorSequence" 
                                            :key="index"
                                            :style="{
                                                width: '40px',
                                                height: '40px',
                                                backgroundColor: color,
                                                border: '2px solid #333',
                                                borderRadius: '4px',
                                                display: 'inline-block'
                                            }"
                                        >
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 填空和选择阶段 -->
                                <div v-else style="text-align:center;" class="clearfix">
                                    <div slot="header" style="margin-bottom: 20px;">
                                        <span style="font-size: 16px; font-weight: bold;">请按顺序选择颜色填入空格：</span>
                                    </div>
                                    
                                    <!-- 显示填空 -->
                                    <div style="display: flex; justify-content: center; gap: 10px; margin: 20px 0;">
                                        <div 
                                            v-for="(answer, index) in qiyiquan_obj.answerList" 
                                            :key="index"
                                            :style="{
                                                width: '40px',
                                                height: '40px',
                                                backgroundColor: answer || '#f0f0f0',
                                                border: index === qiyiquan_obj.currentAnswerIndex ? '3px solid #409EFF' : '2px solid #333',
                                                borderRadius: '4px',
                                                display: 'inline-block',
                                                position: 'relative'
                                            }"
                                        >
                                            <span v-if="!answer" style="
                                                position: absolute;
                                                top: 50%;
                                                left: 50%;
                                                transform: translate(-50%, -50%);
                                                font-size: 18px;
                                                color: #999;
                                            ">?</span>
                                        </div>
                                    </div>
                                    
                                    <!-- 颜色选择按钮 -->
                                    <div style="margin: 20px 0;">
                                        <el-button 
                                            v-for="color in qiyiquan_obj.allColors" 
                                            :key="color"
                                            :style="{
                                                backgroundColor: color,
                                                borderColor: color,
                                                color: 'white',
                                                margin: '5px',
                                                width: '40px',
                                                height: '40px',
                                                minWidth: '40px'
                                            }"
                                            @click="clickQiyiColorItem(color)"
                                        >
                                                
                                        </el-button>
                                    </div>
                                </div>
                                
                                <el-col :span="12" :offset="6">
                                    <el-progress type="circle" :percentage="percentage" :format="() => ''">
                                    </el-progress>
                                </el-col>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
                <!--战斗结算模块-->
                <div class="fight_over" v-show="show_skill == 'fight_over'">
                    <el-row class="skill_info">
                        <el-col :span="24">
                            <el-card class="box-card">
                                <div slot="header" style="text-align:center;" class="clearfix">
                                    <p>从下列战利品盲盒中挑选一个吧</p>
                                </div>
                                <div style="text-align: center;">
                                    <el-button style="margin: 10px;" v-for="(item, index) in mapData[dataIdx].giftList"
                                        :type="item.color" @click="clickGiftItem(item)" plain>
                                        {{ item.text }}
                                    </el-button>

                                </div>

                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <template>
                <el-tabs v-model="activeTab" v-show="show_div == 'story' || show_div == 'road' || show_div == 'shop'" class="custom-tabs">
                    <el-tab-pane label="【故事页】" name="first" v-show="activeTab === 'first'">
                        <!--文字描述模块-故事-->
                        <div class="game_story" v-show="show_div == 'story' || show_div == 'road' || show_div == 'shop'"
                            v-for="item in mapData[dataIdx].paraList">
                            <p>{{ item }}</p>
                        </div>
                        <!--事件选择模块-->
                        <div class="game_select" v-show="show_div == 'story'"
                            v-for="(item, index) in mapData[dataIdx].selectList">
                            <el-button type="text" :id='"idName" + index' v-bind:class='"selectName" + index'
                                @click="to_div(item)">
                                {{ item.text }}
                            </el-button>
                        </div>
                        <!--道路选择模块-->
                        <div class="road_select" v-show="show_div == 'road'" style="text-align: center; ">
                            <el-button v-for="(item, index) in mapData[dataIdx].roadList" :id='"idName" + index'
                                v-bind:class='"roadName" + index' :type="item.color" style="margin: 15px;"
                                @click="toMap(item.to_id)">
                                {{ item.text }}
                            </el-button>
                        </div>
                        <div v-if="mapData[dataIdx].treasureList" class="treasure-container">
                            <el-row :gutter="20">
                                <el-col 
                                    v-for="(item, index) in mapData[dataIdx].treasureList" 
                                    :key="index" 
                                    :span="8"
                                >
                                    <el-button 
                                        class="treasure-button" 
                                        :type="item.color" 
                                        @click="selectTreasure(item)"
                                    >
                                        {{item.text}}
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>
                        <!--奇遇宝箱模块-->
                        <div v-if="show_div == 'story' && mapData[dataIdx].giftList && mapData[dataIdx].giftList.length > 0" class="adventure-treasure-container">
                            <el-divider></el-divider>
                            <div style="text-align: center; margin: 20px 0;">
                                <h4 style="color: #E6A23C; margin-bottom: 15px;">🎁 神秘宝箱 🎁</h4>
                                <p style="margin-bottom: 20px; color: #666;">眼前出现了一个散发着神秘光芒的宝箱...</p>
                                <el-button 
                                    type="warning" 
                                    size="large"
                                    icon="el-icon-present"
                                    @click="openAdventureTreasure()"
                                    :disabled="adventureTreasureOpened"
                                    class="treasure-chest-button"
                                >
                                    {{ adventureTreasureOpened ? '宝箱已开启' : '打开宝箱' }}
                                </el-button>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="【商店】" name="shop" v-if="show_div === 'shop'">
                        <div class="shop-container">
                            <h3 class="shop-title">神秘商店</h3>
                            <p class="shop-money">你的金钱：{{ hero.money }}</p>

                            <el-divider></el-divider>
                            <h4 class="shop-section-title">可购买物品</h4>
                            <div v-if="mapData[dataIdx].shopItems && mapData[dataIdx].shopItems.length > 0" class="shop-item-list">
                                <el-card v-for="itemKey in mapData[dataIdx].shopItems" :key="'buy-' + itemKey" class="shop-item-card" shadow="hover" @click.native="showItemDetail(itemKey)" style="cursor: pointer;">
                                    <div class="item-header">
                                        <span class="item-name">{{ getEquipByName(itemKey).name }}</span>
                                        <el-tag size="mini" type="success">价格: {{ getEquipByName(itemKey).price }}</el-tag>
                                    </div>
                                    <p class="item-desc">{{ getEquipByName(itemKey).desc }}</p>
                                    <el-button type="primary" size="small" @click.stop="buyItem(itemKey)" :disabled="hero.money < getEquipByName(itemKey).price">购买</el-button>
                                </el-card>
                            </div>
                            <div v-else class="empty-shop">
                                暂无商品
                            </div>

                            <el-divider></el-divider>
                            <h4 class="shop-section-title">可出售物品</h4>
                            <div v-if="hero.props.length > 0" class="shop-item-list">
                                <el-card v-for="itemKey in hero.props" :key="'sell-prop-' + itemKey" class="shop-item-card" shadow="hover" @click.native="showItemDetail(itemKey)" style="cursor: pointer;">
                                    <div class="item-header">
                                        <span class="item-name">{{ getEquipName(itemKey) }}</span>
                                        <el-tag size="mini" type="info">售价: {{ Math.floor(getEquipByName(itemKey).price * 0.8) }}</el-tag>
                                    </div>
                                    <p class="item-desc">{{ getEquipByName(itemKey).desc }}</p>
                                    <el-button type="warning" size="small" @click.stop="sellItem(itemKey, 'prop')">出售</el-button>
                                </el-card>
                            </div>
                            <div v-else class="empty-shop">
                                背包是空的，没有可出售的物品
                            </div>

                            <el-divider></el-divider>
                            <div style="text-align: center; margin-top: 20px;">
                                <el-button type="success" @click="to_div({ to_id: mapData[dataIdx].to_id })">离开商店</el-button>
                            </div>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="【状态】" name="status" v-show="activeTab === 'status'">
                        <div class="status-container">
                            <!-- 角色信息区域 -->
                            <div class="character-info-section">
                                <h3 class="status-title">角色信息</h3>
                                <div class="character-stats">
                                    <el-row :gutter="20" class="info-row">
                                        <el-col :span="12">
                                            <div class="info-item">
                                                <span class="info-label">攻击力：</span>
                                                <span class="info-value">{{hero.atk}}</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="12">
                                            <div class="info-item">
                                                <span class="info-label">防御力：</span>
                                                <span class="info-value">{{hero.def}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    
                                    <el-row :gutter="20" class="info-row">
                        <el-col :span="12">
                            <div class="info-item">
                                <span class="info-label">暴击率：</span>
                                <span class="info-value">{{(hero.baoji_rate * 100).toFixed(1)}}%</span>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="info-item">
                                <span class="info-label">吸血：</span>
                                <span class="info-value">{{(hero.lifesteal * 100).toFixed(1)}}%</span>
                            </div>
                        </el-col>
                    </el-row>
                                    
                                    <el-row :gutter="20" class="info-row">
                                        <el-col :span="24">
                                            <div class="info-item">
                                                <span class="info-label">生命值：</span>
                                                <div class="progress-container">
                                                    <el-progress 
                                                        :percentage="setHpProgress(hero.hp)"
                                                        :show-text="false"
                                                        :stroke-width="18"
                                                        :color="getHpColor(hero.hp)"
                                                    ></el-progress>
                                                    <span class="progress-text">{{hero.hp.value}}/{{hero.hp.max}}</span>
                                                </div>
                                            </div>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="20" class="info-row">
                                        <el-col :span="24">
                                            <div class="info-item">
                                                <span class="info-label">金钱：</span>
                                                <span class="info-value money-value">{{hero.money}}</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                            
                            <!-- 装备区域 -->
                            <el-divider></el-divider>
                            <h4 class="status-section-title">当前装备</h4>
                            <div class="equipment-section">
                                <div class="current-equipment">
                                    <div class="equipment-slot">
                                        <div class="slot-info">
                                            <span class="slot-label">武器：</span>
                                            <span class="slot-item" v-if="hero.weapon !== 'none'">{{ getEquipName(hero.weapon) }}</span>
                                            <span class="slot-empty" v-else>无</span>
                                        </div>
                                        <el-button v-if="hero.weapon !== 'none'" type="text" size="mini" @click="unequipWeapon()" class="unequip-btn">卸下</el-button>
                                    </div>
                                    <div class="equipment-slot">
                                        <div class="slot-info">
                                            <span class="slot-label">护具：</span>
                                            <span class="slot-item" v-if="hero.protective !== 'none'">{{ getEquipName(hero.protective) }}</span>
                                            <span class="slot-empty" v-else>无</span>
                                        </div>
                                        <el-button v-if="hero.protective !== 'none'" type="text" size="mini" @click="unequipProtective()" class="unequip-btn">卸下</el-button>
                                    </div>
                                    <div class="equipment-slot">
                                        <div class="slot-info">
                                            <span class="slot-label">护符：</span>
                                            <span class="slot-item" v-if="hero.amulets.length > 0">{{ hero.amulets.length }}/{{ hero.max_amulets }}</span>
                                            <span class="slot-empty" v-else>无</span>
                                        </div>
                                    </div>
                                    <div v-if="hero.amulets.length > 0" class="amulet-list">
                                        <el-tag 
                                            v-for="amulet in hero.amulets" 
                                            :key="amulet" 
                                            size="mini" 
                                            type="warning" 
                                            closable 
                                            @close="removeAmulet(amulet)"
                                            @click="showItemDetail(amulet)"
                                            class="amulet-tag"
                                            style="cursor: pointer;"
                                        >
                                            {{ getEquipName(amulet) }}
                                        </el-tag>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 道具区域 -->
                            <el-divider></el-divider>
                            <h4 class="status-section-title">道具 ({{ hero.props.length }})</h4>
                            <div v-if="hero.props.length > 0" class="bag-item-list">
                                <el-card v-for="itemKey in getSortedProps()" :key="'prop-' + itemKey" class="bag-item-card" shadow="hover" @click.native="showItemDetail(itemKey)" style="cursor: pointer;">
                                    <div class="item-header">
                                        <span class="item-name">{{ getEquipName(itemKey) }}</span>
                                        <el-tag size="mini" :type="getEquipByName(itemKey).type === 'weapon' ? 'danger' : 'info'">{{ getEquipTypeText(getEquipByName(itemKey).type) }}</el-tag>
                                    </div>
                                    <p class="item-desc">{{ getEquipByName(itemKey).desc }}</p>
                                    <div class="item-detail-info">
                                        <small class="detail-text">{{ getEquipDetailInfo(itemKey) }}</small>
                                    </div>
                                    <div class="item-actions">
                                        <el-button v-if="getEquipByName(itemKey).type === 'weapon'" type="primary" size="small" @click.stop="equipWeapon(itemKey)" :disabled="hero.weapon === itemKey">{{ hero.weapon === itemKey ? '已装备' : '装备' }}</el-button>
                                        <el-button v-if="getEquipByName(itemKey).type === 'protective'" type="primary" size="small" @click.stop="equipProtective(itemKey)" :disabled="hero.protective === itemKey">{{ hero.protective === itemKey ? '已装备' : '装备' }}</el-button>
                                        <el-button v-if="getEquipByName(itemKey).type === 'amulet'" type="primary" size="small" @click.stop="equipAmulet(itemKey)" :disabled="getAmuletEquipDisabled(itemKey)">{{ getAmuletEquipButtonText(itemKey) }}</el-button>
                                    </div>
                                </el-card>
                            </div>
                            

                            
                            <div v-if="hero.props.length === 0" class="empty-bag">
                                背包是空的
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </template>
        </div>
        
        <!-- 道具详情弹窗 -->
        <el-dialog
            title="道具详情"
            :visible.sync="itemDetailDialog"
            width="400px"
            center>
            <div v-if="selectedItem" class="item-detail-content">
                <div class="detail-header">
                    <h3 class="item-title">{{ selectedItem.name }}</h3>
                    <el-tag :type="selectedItem.type === 'weapon' ? 'danger' : selectedItem.type === 'protective' ? 'success' : 'warning'" size="medium">
                        {{ getEquipTypeText(selectedItem.type) }}
                    </el-tag>
                </div>
                
                <el-divider></el-divider>
                
                <div class="detail-body">
                    <div class="detail-row">
                        <span class="detail-label">描述：</span>
                        <span class="detail-value">{{ selectedItem.desc }}</span>
                    </div>
                    
                    <div v-if="selectedItem.price" class="detail-row">
                        <span class="detail-label">价格：</span>
                        <span class="detail-value">{{ selectedItem.price }} 金币</span>
                    </div>
                    
                    <div v-if="selectedItem.damage" class="detail-row">
                        <span class="detail-label">伤害：</span>
                        <span class="detail-value">{{ selectedItem.damage }}</span>
                    </div>
                    
                    <div v-if="selectedItem.def" class="detail-row">
                        <span class="detail-label">防御：</span>
                        <span class="detail-value">{{ selectedItem.def }}</span>
                    </div>
                    
                    <div v-if="selectedItem.lifesteal" class="detail-row">
                        <span class="detail-label">吸血：</span>
                        <span class="detail-value">{{ (selectedItem.lifesteal * 100).toFixed(1) }}%</span>
                    </div>
                    
                    <div v-if="selectedItem.level" class="detail-row">
                        <span class="detail-label">等级：</span>
                        <span class="detail-value">{{ selectedItem.level }}</span>
                    </div>
                </div>
            </div>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeItemDetail">关闭</el-button>
            </span>
        </el-dialog>
        
    </transition>
    
    <!-- 死亡页面 -->
     <DeathPage 
         v-if="showDeathPage" 
         :deadHero="deadHeroData" 
         @restart-game="handleRestartGame"
     />
  </div>
</template>
<style scoped>
.custom-tabs >>> .el-tabs__header {
  width: 100%;
}

.custom-tabs >>> .el-tabs__nav-wrap::after {
  height: 0;  /* 移除底部的线 */
}

.custom-tabs >>> .el-tabs__nav {
  width: 100%;
  display: flex;
}

.custom-tabs >>> .el-tabs__item {
  flex: 1;
  text-align: center;
}

.player-info-section {
  padding: 10px 0;
}

.info-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-right: 10px;
  min-width: 60px;
}

.amulets-display {
  flex: 1;
  display: flex;
  align-items: center;
}

.no-equipment {
  color: #909399;
  font-style: italic;
}

.amulet-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.enemy-info-section {
  padding: 10px 0;
}

.enemy-info-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.enemy-card {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.enemy-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.enemy-name {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
}

.enemy-attack {
  font-size: 12px;
  color: #909399;
}

.enemy-hp-row {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.hp-label {
  font-size: 12px;
  color: #606266;
  margin-right: 10px;
  min-width: 60px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-right: 10px;
  min-width: 60px;
}

.info-value {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

/* 自定义进度条样式 */
.el-progress {
  flex: 1;
}

.el-progress /deep/ .el-progress-bar__outer {
  background-color: #f0f2f5;
}

.el-progress /deep/ .el-progress__text {
  font-size: 13px;
  color: #606266;
}

.progress-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;  /* 进度条和文字之间的间距 */
}

.progress-text {
  font-size: 13px;
  color: #606266;
  min-width: 60px;  /* 确保数字有足够空间显示 */
  text-align: right;
}

.el-progress {
  flex: 1;  /* 让进度条占据剩余空间 */
}

.skill-item {
  margin-bottom: 15px;
}

.skill-card {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  overflow: hidden;
  color: #303133;
  transition: .3s;
}

.skill-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.skill-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.skill-content {
  padding: 5px 0;
}

.skill-desc {
  font-size: 14px;
  color: #606266;
  margin: 8px 0;
  line-height: 1.4;
}

.skill-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.skill-stats .el-tag {
  margin-right: 5px;
}

/* 自定义标签颜色 */
.el-tag--mini.el-tag--danger {
  background-color: #fef0f0;
  border-color: #fbc4c4;
  color: #f56c6c;
}

.el-tag--mini.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #faecd8;
  color: #e6a23c;
}

.el-tag--mini.el-tag--success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

.equip-container {
  padding: 10px 0;
}

.equip-section {
  margin-bottom: 20px;
}

.equip-title {
  font-size: 14px;
  color: #606266;
  margin: 0 0 15px 0;
  padding-left: 5px;
  border-left: 3px solid #409EFF;
}

.equip-card {
  margin-bottom: 10px;
  border-radius: 4px;
}

.equip-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.equip-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.equip-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.equip-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.equip-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

/* 状态页面样式 */
.status-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.status-title {
  text-align: center;
  color: #303133;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: bold;
}

.character-info-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.character-stats {
  margin-top: 15px;
}

.money-value {
  color: #E6A23C;
  font-size: 18px;
  font-weight: bold;
}

.status-section-title {
  color: #409EFF;
  margin: 20px 0 15px 0;
  font-size: 18px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.equipment-section {
  margin-bottom: 20px;
}

.current-equipment {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.equipment-slot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  min-width: 250px;
}

.slot-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.unequip-btn {
  color: #f56c6c;
  font-size: 12px;
  padding: 2px 8px;
}

.unequip-btn:hover {
  color: #f56c6c;
  background-color: #fef0f0;
}

.slot-label {
  font-weight: bold;
  color: #495057;
  margin-right: 10px;
  min-width: 50px;
}

.slot-item {
  color: #28a745;
  font-weight: bold;
}

.slot-empty {
  color: #6c757d;
  font-style: italic;
}

.bag-item-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.bag-item-card {
  border-radius: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.bag-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-name {
  font-weight: bold;
  color: #303133;
  font-size: 16px;
}

.item-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 10px 0;
  min-height: 40px;
}

.item-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 15px;
}

.item-detail-info {
  margin: 8px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.detail-text {
  color: #606266;
  font-size: 12px;
  font-weight: 500;
}

.empty-bag {
  text-align: center;
  color: #999;
  padding: 40px 0;
  font-size: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

/* 自定义卡片样式 */
.equip-card.el-card {
  border: 1px solid #ebeef5;
}

.equip-card.el-card:hover {
  border-color: #c6e2ff;
}

.empty-bag {
    text-align: center;
    color: #909399;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;
}

.equip-desc {
    font-size: 12px;
    color: #606266;
    margin: 5px 0;
    line-height: 1.4;
}

.equip-content {
    padding: 5px 0;
}

.amulet-list {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.amulet-tag {
    margin: 2px;
    cursor: pointer;
}

.amulet-tag:hover {
    opacity: 0.8;
}



.treasure-container {
    margin-top: 20px;
}

.treasure-button {
    width: 100%;
    padding: 15px;
    margin-bottom: 10px;
    white-space: normal;
    height: auto;
    line-height: 1.5;
}

/* Shop specific styles */
.shop-container {
    padding: 20px;
}

.shop-story {
    margin-bottom: 20px;
}

.shop-story-paragraph {
    margin-bottom: 10px;
}

.shop-story-paragraph p {
    font-size: 16px;
    line-height: 1.6;
    color: #606266;
    text-align: justify;
    margin: 0;
    padding: 8px 0;
}

.shop-title {
    text-align: center;
    font-size: 24px;
    color: #303133;
    margin-bottom: 20px;
}

.shop-money {
    text-align: center;
    font-size: 18px;
    color: #606266;
    margin-bottom: 20px;
    font-weight: bold;
}

.shop-section-title {
    font-size: 16px;
    color: #303133;
    margin: 20px 0 15px 0;
    padding-left: 5px;
    border-left: 3px solid #67c23a;
}

.shop-item-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.shop-item-card {
    border-radius: 4px;
    border: 1px solid #ebeef5;
    background-color: #fff;
    overflow: hidden;
    color: #303133;
    transition: .3s;
    padding: 15px;
}

.shop-item-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.shop-item-card .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.shop-item-card .item-name {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.shop-item-card .item-desc {
    font-size: 13px;
    color: #909399;
    margin: 8px 0;
    line-height: 1.4;
}

.empty-shop {
    text-align: center;
    color: #909399;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* 攻击和被攻击动效 */
@keyframes attack-shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

@keyframes damage-flash {
    0% { background-color: transparent; }
    50% { background-color: rgba(245, 108, 108, 0.3); }
    100% { background-color: transparent; }
}

@keyframes attack-glow {
    0% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(64, 158, 255, 0.8), 0 0 30px rgba(64, 158, 255, 0.6); }
    100% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.3); }
}

@keyframes enemy-attack-glow {
    0% { box-shadow: 0 0 5px rgba(245, 108, 108, 0.3); }
    50% { box-shadow: 0 0 20px rgba(245, 108, 108, 0.8), 0 0 30px rgba(245, 108, 108, 0.6); }
    100% { box-shadow: 0 0 5px rgba(245, 108, 108, 0.3); }
}

@keyframes enemy-attack-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes hit-bounce {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.attack-animation {
    animation: attack-shake 0.3s ease-in-out, attack-glow 0.6s ease-in-out;
    border: 3px solid #67c23a !important;
    box-shadow: 0 0 15px rgba(103, 194, 58, 0.6) !important;
}

.enemy-attack-animation {
    animation: attack-shake 0.3s ease-in-out, enemy-attack-glow 0.6s ease-in-out, enemy-attack-pulse 0.6s ease-in-out;
    border: 3px solid #67c23a !important;
    box-shadow: 0 0 15px rgba(103, 194, 58, 0.6) !important;
}

.damage-animation {
    animation: damage-flash 0.5s ease-in-out;
}

.hit-animation {
    animation: hit-bounce 0.5s ease-in-out;
}

.skill-disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    filter: grayscale(100%) !important;
}

/* 道具详情弹窗样式 */
.item-detail-content {
    padding: 10px;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.item-title {
    margin: 0;
    color: #303133;
    font-size: 18px;
    font-weight: bold;
}

.detail-body {
    padding: 10px 0;
}

/* 奇遇宝箱样式 */
.adventure-treasure-container {
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
    border-radius: 12px;
    border: 2px solid #E6A23C;
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.2);
}

.treasure-chest-button {
    font-size: 16px;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(230, 162, 60, 0.3);
}

.treasure-chest-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(230, 162, 60, 0.4);
    animation: treasure-glow 1.5s infinite;
}

.treasure-chest-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    animation: none;
}

@keyframes treasure-glow {
    0%, 100% {
        box-shadow: 0 6px 16px rgba(230, 162, 60, 0.4);
    }
    50% {
        box-shadow: 0 6px 20px rgba(230, 162, 60, 0.6), 0 0 25px rgba(255, 215, 0, 0.3);
    }
}

.detail-row {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;
}

.detail-label {
    font-weight: bold;
    color: #606266;
    min-width: 60px;
    margin-right: 10px;
}

.detail-value {
    color: #303133;
    flex: 1;
    word-break: break-word;
}
</style>
