import { equipList } from '../equip.js';
import { skillList } from '../skill.js';

export const GiftType = {
    SKILL: 'skill',
    EQUIP: 'equip',
    NONE: 'none'
};

export const GiftColor = {
    SUCCESS: 'success',
    PRIMARY: 'primary',
    DANGER: 'danger',
    WARNING: 'warning'
};

export function createGift(name, type = GiftType.NONE) {
    return {
        name,
        type
    };
}

export function createRecoveryGift(minRecover, maxRecover) {
    return {
        text: '回复盲盒',
        type: 1,
        color: GiftColor.SUCCESS,
        tips: '回复随机数量的生命值',
        info: [minRecover, maxRecover]
    };
}

export function createAttributeGift(hp, mp, atk) {
    return {
        text: '属性盲盒',
        type: 2,
        color: GiftColor.PRIMARY,
        tips: '随机提升一点属性（最大生命值、攻击力）',
        info: [hp, mp, atk]
    };
}

export function createSkillGift(chance = 0.2, level = 1) {
    const allSkills = Object.entries(skillList)
        .filter(([key, skill]) => skill.level === level && key !== 'ack' && key !== 'wudi')
        .map(([key, skill]) => ({
            chance,
            name: key,
            type: GiftType.SKILL
        }));

    const shuffled = allSkills.sort(() => 0.5 - Math.random());
    
    return createProbabilityGift(
        [shuffled[0]],
        '技能盲盒',
        3,
        GiftColor.DANGER,
        '获得一个技能（概率较低）'
    );
}

export function createEquipGift(chance = 0.2, level = 1) {
    const allItems = [
        ...Object.entries(equipList)
            .filter(([key, item]) => item.level === level)
            .map(([key]) => ({
                chance,
                name: key,
                type: GiftType.EQUIP
            })),

    ];

    const shuffled = allItems.sort(() => 0.5 - Math.random());
    
    return createProbabilityGift(
        [shuffled[0]],
        '护符盲盒',
        4,
        GiftColor.WARNING,
        '随机获得一个武器或道具（概率较低）'
    );
}

export function createProbabilityGift(items, text, type, color, tips = null) {
    return {
        text,
        type,
        color,
        tips: tips || `获得一个${text}（概率较低）`,
        info: [
            ...items,
            {
                chance: 1,
                name: 'none',
                type: GiftType.NONE
            }
        ]
    };
}