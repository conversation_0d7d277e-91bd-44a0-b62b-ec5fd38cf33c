package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
)

// AnalyticsEvent 打点事件结构体
type AnalyticsEvent struct {
	ID        int       `json:"id" db:"id"`
	Type      int       `json:"type" db:"type"`           // 1: 开始游戏, 2: 重新开始
	Timestamp time.Time `json:"timestamp" db:"timestamp"` // 打点时间
	Source    string    `json:"source" db:"source"`       // 来源信息
	Level     int       `json:"level" db:"level"`         // 死亡关卡（type=2时有效）
	UserAgent string    `json:"user_agent" db:"user_agent"` // 用户代理
	IP        string    `json:"ip" db:"ip"`               // 用户IP
	Extra     string    `json:"extra" db:"extra"`         // 额外信息（JSON格式）
}

// AnalyticsRequest 前端发送的打点请求
type AnalyticsRequest struct {
	Type   int                    `json:"type" binding:"required"`
	Source string                 `json:"source"`
	Level  int                    `json:"level"`
	Extra  map[string]interface{} `json:"extra"`
}

var db *sql.DB

func main() {
	// 初始化数据库连接
	var err error
	// 注意：请根据实际情况修改数据库连接字符串
	dsn := "root:password@tcp(localhost:3306)/word_game_analytics?charset=utf8mb4&parseTime=True&loc=Local"
	db, err = sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err = db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	// 创建表（如果不存在）
	if err = createTable(); err != nil {
		log.Fatal("Failed to create table:", err)
	}

	// 初始化Gin路由
	r := gin.Default()

	// 配置CORS
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{"http://localhost:8080", "http://127.0.0.1:8080"} // 允许前端域名
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	r.Use(cors.New(config))

	// 路由定义
	r.POST("/api/analytics", handleAnalytics)
	r.GET("/api/analytics/stats", handleStats)
	r.GET("/health", handleHealth)

	// 启动服务器
	log.Println("Analytics server starting on :8081")
	if err := r.Run(":8081"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

// createTable 创建数据库表
func createTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS analytics_events (
		id INT AUTO_INCREMENT PRIMARY KEY,
		type INT NOT NULL COMMENT '事件类型: 1=开始游戏, 2=重新开始',
		timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '打点时间',
		source VARCHAR(255) DEFAULT '' COMMENT '来源信息',
		level INT DEFAULT 0 COMMENT '死亡关卡（type=2时有效）',
		user_agent TEXT COMMENT '用户代理',
		ip VARCHAR(45) COMMENT '用户IP',
		extra JSON COMMENT '额外信息',
		INDEX idx_type (type),
		INDEX idx_timestamp (timestamp),
		INDEX idx_level (level)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏打点事件表';
	`
	_, err := db.Exec(query)
	return err
}

// handleAnalytics 处理打点请求
func handleAnalytics(c *gin.Context) {
	var req AnalyticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	// 验证事件类型
	if req.Type != 1 && req.Type != 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event type, must be 1 or 2"})
		return
	}

	// 获取客户端信息
	userAgent := c.GetHeader("User-Agent")
	clientIP := c.ClientIP()

	// 处理额外信息
	var extraJSON string
	if req.Extra != nil {
		extraBytes, err := json.Marshal(req.Extra)
		if err != nil {
			log.Printf("Failed to marshal extra data: %v", err)
			extraJSON = "{}"
		} else {
			extraJSON = string(extraBytes)
		}
	} else {
		extraJSON = "{}"
	}

	// 插入数据库
	query := `
		INSERT INTO analytics_events (type, source, level, user_agent, ip, extra)
		VALUES (?, ?, ?, ?, ?, ?)
	`
	result, err := db.Exec(query, req.Type, req.Source, req.Level, userAgent, clientIP, extraJSON)
	if err != nil {
		log.Printf("Failed to insert analytics event: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save event"})
		return
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		log.Printf("Failed to get last insert ID: %v", err)
	}

	log.Printf("Analytics event saved: ID=%d, Type=%d, Source=%s, Level=%d, IP=%s",
		id, req.Type, req.Source, req.Level, clientIP)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"id":      id,
		"message": "Event recorded successfully",
	})
}

// handleStats 获取统计信息
func handleStats(c *gin.Context) {
	// 获取基本统计信息
	stats := make(map[string]interface{})

	// 总事件数
	var totalEvents int
	err := db.QueryRow("SELECT COUNT(*) FROM analytics_events").Scan(&totalEvents)
	if err != nil {
		log.Printf("Failed to get total events: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get statistics"})
		return
	}
	stats["total_events"] = totalEvents

	// 按类型统计
	typeStats := make(map[string]int)
	rows, err := db.Query("SELECT type, COUNT(*) FROM analytics_events GROUP BY type")
	if err != nil {
		log.Printf("Failed to get type stats: %v", err)
	} else {
		defer rows.Close()
		for rows.Next() {
			var eventType, count int
			if err := rows.Scan(&eventType, &count); err == nil {
				switch eventType {
				case 1:
					typeStats["game_start"] = count
				case 2:
					typeStats["game_restart"] = count
				}
			}
		}
	}
	stats["by_type"] = typeStats

	// 今日统计
	var todayEvents int
	err = db.QueryRow("SELECT COUNT(*) FROM analytics_events WHERE DATE(timestamp) = CURDATE()").Scan(&todayEvents)
	if err != nil {
		log.Printf("Failed to get today events: %v", err)
	} else {
		stats["today_events"] = todayEvents
	}

	c.JSON(http.StatusOK, stats)
}

// handleHealth 健康检查
func handleHealth(c *gin.Context) {
	// 检查数据库连接
	if err := db.Ping(); err != nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"status": "unhealthy",
			"error":  "Database connection failed",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
