{"name": "word-game", "description": "A Vue.js project", "version": "1.0.0", "author": "y<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --open --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules"}, "dependencies": {"element-ui": "^2.0.0-rc.1", "lib-flexible": "^0.3.2", "postcss-plugin-px2rem": "^0.8.1", "vue": "^2.5.11"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-preset-env": "^1.6.0", "babel-preset-stage-3": "^6.24.1", "cross-env": "^5.0.5", "css-loader": "^0.28.11", "file-loader": "^1.1.4", "style-loader": "^3.3.1", "vue-loader": "^13.0.5", "vue-template-compiler": "^2.4.4", "webpack": "^3.6.0", "webpack-dev-server": "^2.9.1"}}