<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>文字冒险游戏</title>
    <script type="text/javascript" src="js/vue.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
</head>
<body>
    
    <div id="root">
        <button v-on:click="show_page++">
            Toggle
          </button>
          <button v-on:click="show_page--">
            Toggle
          </button>
          <!-- 游戏起始页 -->
          <transition name="fade">
            <div id = 'start_page' v-show="show_page==100">
                <el-row :gutter="20" :offset="8">
                    <el-col :span="12" :offset="8">
                        <p>浮生万象</p>
                    </div></el-col>
                </el-row>
            </div>
          </transition>
        <!-- 场景起始页 -->
          <transition name="fade">
            <div id = 'start_page_scene' v-show="show_page==101">
                <p>夜夜原</p>
            </div>
          </transition>
    </div>


    <script type="text/javascript">
        
        const v = new Vue({
            el:'#root',
            data:function(){
                return{
                    timeReserve:100,
                    show_page: 100, 
                }
            },
            methods:{
                show_page_add()
                {
                    this.show_page++;
                },
                val_reduce()
                {
                    this.percentage1--;
                },
                return_percentage()
                {
                    //this.percentage1--;
                    setInterval(this.val_reduce,1000)  //每隔一秒钟打印出111
                }  
            }
        });
        
    </script>
    <style>
        .fade-enter-active, .fade-leave-active {
          transition: opacity .5s;
        }
        .fade-enter, 
        .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
          opacity: 0;
        }
        .p{
            font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
            text-align: center;
        }
    </style>
</body>

</html>