function getEnemyByName(name)
{
    switch(name)
    {
        case 'yeyeyuan_yelang':
        return {
            name:'野狼',
            hp:{
                value:25,
                max:25,            },
            atk:5
        }
        case 'yeyeyuan_yelangplus':
        return {
            name:'野狼头目',
            hp:{
                value:75,
                max:75,
            },
            atk:10
        } 
        
        // 东面 - 植物属性怪物
        case 'dong_xiaocao':
            return {
                name:'小草',
                hp:{
                    value:30,
                    max:30,
                },
                atk:5
            }
        case 'dong_zhuzi':
            return {
                name:'竹子',
                hp:{
                    value:35,
                    max:35,
                },
                atk:5
            }
        case 'dong_xiaohua':
            return {
                name:'小花',
                hp:{
                    value:35,
                    max:35,
                },
                atk:5
            }
        case 'dong_dashu':
            return {
                name:'大树',
                hp:{
                    value:45,
                    max:45,
                },
                atk:10
            }
        case 'dong_yecong':
            return {
                name:'野丛',
                hp:{
                    value:40,
                    max:40,
                },
                atk:7
            }
        case 'dong_dashu_elite':
            return {
                name:'大树精',
                hp:{
                    value:90,
                    max:90,
                },
                atk:15
            }
        case 'dong_huayao_elite':
            return {
                name:'花妖',
                hp:{
                    value:110,
                    max:110,
                },
                atk:20
            }
        case 'dong_shuwang_elite':
            return {
                name:'树王',
                hp:{
                    value:125,
                    max:125,
                },
                atk:25
            }
        
        // 西面 - 动物属性怪物
        case 'xi_xiaohu':
            return {
                name:'小虎',
                hp:{
                    value:40,
                    max:40,
                },
                atk:7
            }
        case 'xi_yeniu':
            return {
                name:'野牛',
                hp:{
                    value:40,
                    max:40,
                },
                atk:7
            }
        case 'xi_huoli':
            return {
                name:'火狸',
                hp:{
                    value:35,
                    max:35,
                },
                atk:5
            }
        case 'xi_shantu':
            return {
                name:'山兔',
                hp:{
                    value:40,
                    max:40,
                },
                atk:5
            }
        case 'xi_yelong':
            return {
                name:'青狼',
                hp:{
                    value:40,
                    max:40,
                },
                atk:10
            }
        case 'xi_anyinglang_elite':
            return {
                name:'暗影狼',
                hp:{
                    value:100,
                    max:100,
                },
                atk:15
            }
        case 'xi_chiyanlang_elite':
            return {
                name:'炽焰狼',
                hp:{
                    value:120,
                    max:120,
                },
                atk:20
            }
        case 'xi_yeyelang_elite':
            return {
                name:'夜夜狼',
                hp:{
                    value:140,
                    max:140,
                },
                atk:25
            }
        
        // 南面 - 冰雪属性怪物
        case 'nan_xueren':
            return {
                name:'雪人',
                hp:{
                    value:30,
                    max:30,
                },
                atk:5
            }
        case 'nan_bingkuai':
            return {
                name:'冰块',
                hp:{
                    value:40,
                    max:40,
                },
                atk:7
            }
        case 'nan_xuefeng':
            return {
                name:'雪蜂',
                hp:{
                    value:35,
                    max:35,
                },
                atk:5
            }
        case 'nan_bingzhu':
            return {
                name:'冰柱',
                hp:{
                    value:40,
                    max:40,
                },
                atk:7
            }
        case 'nan_xuequiu':
            return {
                name:'雪球',
                hp:{
                    value:40,
                    max:50,
                },
                atk:7
            }
        case 'nan_bingwang_elite':
            return {
                name:'冰王',
                hp:{
                    value:110,
                    max:110,
                },
                atk:15
            }
        case 'nan_xuewang_elite':
            return {
                name:'雪王',
                hp:{
                    value:130,
                    max:130,
                },
                atk:20
            }
        case 'nan_bingxue_elite':
            return {
                name:'冰雪王',
                hp:{
                    value:150,
                    max:150,
                },
                atk:25
            }
        
        // 北面 - 幽灵属性怪物
        case 'bei_xiaogui':
            return {
                name:'小鬼',
                hp:{
                    value:25,
                    max:25,
                },
                atk:5
            }
        case 'bei_youhun':
            return {
                name:'游魂',
                hp:{
                    value:35,
                    max:35,
                },
                atk:7
            }
        case 'bei_yegui':
            return {
                name:'野鬼',
                hp:{
                    value:40,
                    max:40,
                },
                atk:7
            }
        case 'bei_egui':
            return {
                name:'恶鬼',
                hp:{
                    value:40,
                    max:50,
                },
                atk:10
            }
        case 'bei_guiying':
            return {
                name:'鬼影',
                hp:{
                    value:60,
                    max:60,
                },
                atk:10
            }
        case 'bei_guiwang_elite':
            return {
                name:'鬼王',
                hp:{
                    value:120,
                    max:120,
                },
                atk:15
            }
        case 'bei_guishen_elite':
            return {
                name:'鬼神',
                hp:{
                    value:140,
                    max:140,
                },
                atk:20
            }
        case 'bei_guizhu_elite':
            return {
                name:'鬼主',
                hp:{
                    value:160,
                    max:160,
                },
                atk:25
            }
        
        // Boss怪物
        case 'gushu_boss':
            return {
                name:'古树之王',
                hp:{
                    value:350,
                    max:350,
                },
                atk:30
            }
        case 'langwang_boss':
            return {
                name:'狼王',
                hp:{
                    value:400,
                    max:400,
                },
                atk:35
            }
        case 'xueshanshen_boss':
            return {
                name:'雪山神',
                hp:{
                    value:450,
                    max:450,
                },
                atk:40
            }
        case 'wangsheng_boss':
            return {
                name:'往生之主',
                hp:{
                    value:500,
                    max:500,
                },
                atk:45
            }
        
        break;
        default:
            console.warn('未找到敌人:', name);
            return {
                name: '未知敌人',
                hp: {
                    value: 1,
                    max: 1
                },
                atk: 1
            };
            break;
    }
}  
export {getEnemyByName}
