<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>vue测试</title>
    <script type="text/javascript" src="js/vue.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 引入组件库 -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
</head>
<body>
    <div id="root">
        <h1>hello,{{stu.name}}</h1>
        <input v-bind:value="url"><!-- v-bind:单向绑定，只能由data流向页面 ；v-bind:可以简写为：-->
        <input v-model:value="url"><!-- v-model:双向绑定;但是只能应用在表单类元素上 都有value值 ；v-model:value可以简写为v-model-->
        <button v-on:click="showinfo">不传参</button>
        <button v-on:click="showinfo2(66,$event)">传参</button>

        <br/>
        <i class="el-icon-edit"></i>
        <i class="el-icon-share"></i>
        <i class="el-icon-delete"></i>
        <el-button type="primary" icon="el-icon-search">搜索</el-button>
        <br/>
        <el-progress type="dashboard" :percentage="percentage1"></el-progress>
        <button v-on:click="return_percentage">倒计时</button>
        <br/>
        <button v-on:click="show++">
            Toggle
          </button>
          <transition name="fade"><!-- 测试淡入、淡出 -->
            <p v-if="show==5">hello</p>
          </transition>
    </div>
    <script type="text/javascript">
        
        const v = new Vue({
            el:'#root',
            // data:{//第一种写法
            //     stu:{
            //         name:'ymm',
            //         age:12
            //     },
            //     url:'http://www.baidu.com'
            // }
            data:function(){//不能写成箭头函数，否则this就是window了。普通函数的this是vue对象
                return{
                    stu:{
                        name:'ymm',//原理：数据代理
                        age:12
                    },
                    url:'http://www.baidu.com',
                    percentage1:85,
                    show: 1
                    
                }
            },
            methods:{
                showinfo(){
                    alert("11")
                },
                showinfo2(number,event){
                    alert(number);
                    console.log(event);
                },
                val_reduce()
                {
                    this.percentage1--;
                },
                return_percentage()
                {
                    //this.percentage1--;
                    setInterval(this.val_reduce,1000)  //每隔一秒钟打印出111
                }
                
            }
        });
        console.log(v);
        //v.$mount('#root');//代替el:'#root',
    </script>
    <style>
        .fade-enter-active, .fade-leave-active {
          transition: opacity .5s;
        }
        .fade-enter, 
        .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
          opacity: 0;
        }
    </style>
</body>
</html>