function getParaByName(_name) {
    switch (_name) {
        case 'taibai_shouce':
            return [
                '《太白剑诀使用手册》',
                '\"----剑圣太白曾凭此剑决一剑破开夜夜原之门。\"',
                '年轻的旅人呀，也许你现在还没有意识到自己捡到了多么珍贵的东西，但随着接下来的战斗以及未来漫长无比的冒险，你将会了解到它的重要性。',
                '它的使用很简单，在吟唱技能后会有两个空位来需要你填写，你只需要在备选的汉字中依此选出空位上缺失的文字就可以成功发动技能（正确的越多伤害越高）。',
                '【注】配合部分道具可使其威力呈指数级增长。',
                '（以下内容可跳过）',
                '剑诀吟唱完毕后，你的身影将会化作无数道残影，各持一柄虚无之剑对在场的所有敌人造成巨额伤害。',
                '啊，你还没有剑吗？没关系，隔壁故事里那个用六脉神剑的也没有剑呀！',
                '年轻的旅人呀，你已经掌握了太白剑诀的基本使用技巧，接下来，转过身，去正面面对你的敌人吧！'
            ]
        break;
        case 'jiuzhang_shouce':
            return [
                '《九章掌法使用手册》',
                '"----先贤张苍曾凭此掌法一掌破开夜夜原之门。"',
                '年轻的旅人呀，也许你现在还没有意识到自己捡到了多么珍贵的东西，但随着接下来的战斗以及未来漫长无比的冒险，你将会了解到它的重要性。',
                '它的使用很简单，在吟唱技能后会有一个给出得数的等式来需要你补全，你只需要在备选的数值中选出指定个数的数字来使给出的等式成立，就可以成功发动技能。',
                '【注】配合部分道具可使其威力呈指数级增长。',
                '（以下内容可跳过）',
                '心法吟唱完毕后，你的将对正前方第一个存活的敌人拍出一掌，对其造成巨额伤害。',
                '啊。你数学不太好吗？没关系，这套掌法只需要用到小学一年级的知识呦。',
                '年轻的旅人呀，你已经掌握了九章掌法的基本使用技巧，接下来，转过身，去正面面对你的敌人吧！'
            ];
        break;
        case 'qiyi_shouce':
            return [
                '《七益拳使用手册》',
                '"----将军七益曾凭此拳法一拳破开夜夜原之门。"',
                '年轻的旅人呀，也许你现在还没有意识到自己捡到了多么珍贵的东西，但随着接下来的战斗以及未来漫长无比的冒险，你将会了解到它的重要性。',
                '它的使用很简单，在吟唱技能后会有几个不同颜色的方块，记住它！然后在它消失后按照刚才颜色出现的顺序依次选择颜色，就可以成功发动技能（正确的越多伤害越高）。',
                '【注】配合部分道具可使其威力呈指数级增长。',
                '（以下内容可跳过）',
                '拳法吟唱完毕后，你将对所有敌人发出一记威力巨大的拳击，对其造成巨额伤害，同时还能回复自身不少的血量。',
                '杀敌一千，自增八百，真是痛快！',
                '年轻的旅人呀，你已经掌握了七益拳的基本使用技巧，接下来，转过身，去正面面对你的敌人吧！'
            ];
        break;
        default: return [];
        break;
    }
}

export { getParaByName}