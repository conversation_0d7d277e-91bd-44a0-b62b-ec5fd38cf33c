p{
  font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Aria<PERSON>,sans-serif;
  line-height: 30px;
}
.box-card {
  font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
}
.main_titile{
    position: absolute;
    top:40%;
    left:50%;
    width:200px;
    transform: translate(-50%,-50%);
}
.start_title
{
    width:200px;
    text-align:center;
    letter-spacing: 7px;
}
.start_link
{  
    font-size:17px !important;
    width:200px;
    text-align:center;
}


.hp_div{
  display:flex;
}
.skill_list{
  margin-top: 20px;
}
.el-progress{
  width:60%;
  align-self:center;
}
.fighting_main{
  min-height: 400px;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  margin: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.fight_info {
  height: auto;
  min-height: 180px;
  margin-bottom: 20px;
}

.fight_info .el-card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.fight_info .el-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.fight_info .el-card__header {
  background: #f5f5f5;
  color: #333;
  font-weight: 500;
  text-align: center;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 16px;
}

.fight_info .el-card__body {
  padding: 16px;
}
.el-card__body
{
  padding-left:  30px;
}

.game_story
{
  text-indent:2em;
  font-size: medium;
}
.road_select
{
}
.game_select
{
  text-indent:4em;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, 
.fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

#footer {
  position: absolute;
    top:95%;
    left:50%;
    width:200px;
    transform: translate(-50%,-50%);
}
