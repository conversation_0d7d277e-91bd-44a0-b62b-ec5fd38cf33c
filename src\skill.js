// 技能列表
const skillList = {
    'ack': {
        level: 0,
        name: '普通攻击',
        yName: 'ack',
        desc: '对敌人造成100%攻击力的伤害',
        hurt: 1,
        level: 1,
        objType: 'single'  // single/all 单体/群体
    },
    'wudi': {
        level: 110,
        name: '无敌',
        yName: 'wudi',
        desc: '造成巨额伤害',
        hurt: 550,
        level: 1,
        objType: 'all'
    },
    'taibai_j': {
        level: 1,
        name: '太白剑诀',
        yName: 'taibai_j',
        desc: '太白剑诀：对敌人全体造成较高伤害（每级伤害+30%）',
        hurt: 3,
        level: 1,
        objType: 'all',
        add: {
            type: 'poem',
            name: 'libai'
        }
    },
    'jiuzhang_z': {
        level: 1,
        name: '九章掌法',
        yName: 'jiuzhang_z',
        desc: '九章掌法:造成巨额伤害（每级伤害+30%）',
        hurt: 20,
        level: 1,
        objType: 'single'
    },
    'qiyi_q': {
        level: 1,
        name: '七益拳',
        yName: 'qiyi_q',
        desc: '七益拳：造成伤害后恢复伤害值30%的血量（每级伤害+30%）',
        hurt: 7,
        level: 1,
        objType: 'single',
        healPercent: 0.3  // 恢复伤害值30%的血量
    }
};

// 获取技能信息
export function getSkillByName(_name) {
    const skill = skillList[_name];
    if (skill) {
        // 返回技能对象的深拷贝，确保每个英雄都有独立的技能实例
        return JSON.parse(JSON.stringify(skill));
    }
    return null;
}

function getPoemArr(_type) {
    switch (_type) {
        case 'libai':
            return [
                '蓬莱文章建安骨，中间小谢又清发。',
                '日照香炉生紫烟，遥看瀑布挂前川。',
                '飞流直下三千尺，疑是银河落九天。',
                '朝辞白帝彩云间，千里江陵一日还。',
                '两岸猿声啼不住，轻舟已过万重山。',
                '故人西辞黄鹤楼，烟花三月下扬州。',
                '孤帆远影碧空尽，唯见长江天际流。',
                '天生我才必有用，千金散尽还复来。',
                '长风破浪会有时，直挂云帆济沧海。',
                '仰天大笑出门去，我辈岂是蓬蒿人。',
                '大鹏一日同风起，扶摇直上九万里。',
                '早知如此绊人心，何若当初无相识。',
                '一为迁客去长沙，西望长安不见家。',
                '黄鹤楼中吹玉笛，江城五月落梅花。',
                '天门中断楚江开，碧水东流至此回。',
                '两岸青山相对出，孤帆一片日边来。',
                '金樽清酒斗十千，玉盘珍羞直万钱。',
                '停杯投箸不能食，拔剑四顾心茫然。',
                '人生在世不称意，明朝散发弄扁舟。',
                '古来圣贤皆寂寞，惟有饮者留其名。',
                '桃花潭水深千尺，不及汪伦送我情。',
                '天门中断楚江开，碧水东流至此回。',
                '两岸青山相对出,孤帆一片日边来。',
                '欲渡黄河冰塞川，将登太行雪满山。',
                '杨花落尽子规啼，闻道龙标过五溪。',
                '我寄愁心与明月，随风直到夜郎西。',
                '平林漠漠烟如织，寒山一带伤心碧',
                '床前明月光，疑是地上霜。',
                '举头望明月，低头思故乡。',
                '渡远荆门外，来从楚国游。',
                '山随平野尽，江入大荒流。',
                '月下飞天镜，云生结海楼。',
                '举杯邀明月，对影成三人。',
                '危楼高百尺，手可摘星辰。',
                '不敢高声语，恐惊天上人。',
                '赵客缦胡缨，吴钩霜雪明。',
                '银鞍照白马，飒沓如流星。',
                '十步杀一人，千里不留行。',
                '事了拂衣去，深藏身与名。',

            ];
            break;
        case 'dufu':
            return [
                '两个黄鹂鸣翠柳，一行白鹭上青天。',
                '窗含西岭千秋雪，门泊东吴万里船。',
                '风急天高猿啸哀，渚清沙白鸟飞回。',
                '无边落木萧萧下，不尽长江滚滚来。',
                '万里悲秋常作客，百年多病独登台。',
                '艰难苦恨繁霜鬓，潦倒新停浊酒杯。',
                '剑外忽传收蓟北，初闻涕泪满衣裳。',
                '却看妻子愁何在，漫卷诗书喜欲狂。',
                '白日放歌须纵酒，青春作伴好还乡。',
                '即从巴峡穿巫峡，便下襄阳向洛阳。',
                '丞相祠堂何处寻，锦官城外柏森森。',
                '映阶碧草自春色，隔叶黄鹂空好音。',
                '三顾频烦天下计，两朝开济老臣心。',
                '出师未捷身先死，长使英雄泪满襟。',
                '岱宗夫如何，齐鲁青未了。',
                '造化钟神秀，阴阳割昏晓。',
                '荡胸生曾云，决眦入归鸟。',
                '会当凌绝顶，一览众山小。',
                '国破山河在，城春草木深。',
                '感时花溅泪，恨别鸟惊心。',
                '烽火连三月，家书抵万金。',
                '好雨知时节，当春乃发生。',
                '随风潜入夜，润物细无声。',

            ];
        default:
            break;
    }
}

// 获取太白剑诀的诗句
export function getPoemObj(_poem_arr) {
    var poemArr = [];
    for (let ite of _poem_arr) {
        poemArr = poemArr.concat(getPoemArr(ite));
    }
    var idx = Math.floor(Math.random() * poemArr.length);
    var poem = poemArr[idx];
    var halfIdx = (poem.length - 2) / 2;
    var posList = [];
    posList[0] = Math.floor(Math.random() * halfIdx);
    if (poem[posList[0]] == ',') {
        posList[0] -= 1;
    }
    posList[1] = Math.floor(Math.random() * halfIdx) + halfIdx + 1;
    if (poem[posList[1]] == '。') {
        posList[1] -= 1;
    }
    var blankList = [
        poem[posList[0]],
        poem[posList[1]],
    ];

    var replaceStrArr = [
        poem.substring(0, posList[0]),
        poem.substring(posList[0] + 1, posList[1]),
        poem.substring(posList[1] + 1, poemArr.length),
    ];
    // 使用同一句诗生成选项
    var blurList = poem.split('');
    // 确保正确答案一定在选项中
    // 先检查正确答案是否已经在blurList中
    if (!blurList.includes(blankList[0])) {
        // 如果第一个答案不在列表中，随机替换一个位置
        blurList[Math.floor(Math.random() * blurList.length)] = blankList[0];
    }
    if (!blurList.includes(blankList[1])) {
        // 如果第二个答案不在列表中，随机替换一个位置
        blurList[Math.floor(Math.random() * blurList.length)] = blankList[1];
    }
    
    // 打乱选项顺序
    for (let i = blurList.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [blurList[i], blurList[j]] = [blurList[j], blurList[i]];
    }
    
    var anserList = [
        '__',
        '__'
    ];
    return { poem: poem, posList: posList, blankList: blankList, replaceStrArr: replaceStrArr, anserList: anserList, blurList: blurList };
}

// 获取九章掌法的数据
export function getJiuzhangSource(num_size, removeZero = false) {
    var total = 0;
    var leftList = [];
    var anserList1 = [];
    for (let i = 0; i < num_size; i++) {
        let nbreak = true;
        while (nbreak) {
            let temp = Math.floor(Math.random() * 9);
            // 如果装备了无零珠，则不生成0
            if (removeZero && temp === 0) {
                continue;
            }
            let equal = false;
            for (let ite of leftList) {
                if (ite == temp) {
                    equal = true;
                    break;
                }
            }
            if (!equal) {
                leftList.push(temp);
                nbreak = false;
            }
        }

    }
    for (let ite = 0; ite < leftList.length; ite++) {
        total += leftList[ite];
        anserList1.push('__');
    }
    return { total: total, leftList: leftList, anserList: anserList1 }
}

// 获取七益拳的数据
export function getQiyiSource(num_size) {
    // 定义7种颜色
    const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink'];
    
    // 生成指定数量的随机颜色的方块序列
    var colorSequence = [];
    for (let i = 0; i < num_size; i++) {
        colorSequence.push(colors[i % colors.length]);
    }
    
    // 打乱颜色序列
    for (let i = colorSequence.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [colorSequence[i], colorSequence[j]] = [colorSequence[j], colorSequence[i]];
    }
    
    // 创建答案数组（初始为空）
    var answerList = new Array(num_size).fill('');
    
    return { 
        colorSequence: colorSequence,
        answerList: answerList,
        allColors: colors
    }
}

// 导出技能列表
export { skillList };