<script>
import analytics from './analytics.js'

export default {
  data() {
    return {
    }
  },
  methods: {
    // 开始游戏
    startGame() {
      // 发送游戏开始打点
      analytics.trackGameStart({
        button_clicked: 'start_game',
        page: 'main_start',
        game_version: '1.0.0'
      }).catch(error => {
        console.warn('Failed to track game start:', error);
      });

      // 触发原有的事件
      this.$emit('childEvent', 101);
    }
  }
}
</script>

<template>
<div class = 'main_titile'>
  
    <h1 class = 'start_title'>夏日梦境</h1>
    <el-button class = 'start_link' @click="startGame" type="text">进入</el-button>
    
</div>

</template>