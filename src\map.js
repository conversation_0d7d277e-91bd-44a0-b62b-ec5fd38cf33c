import {getParaByName} from "./storyPara.js"
import { createGift, GiftType, createRecoveryGift, createAttributeGift, createProbabilityGift, createSkillGift, createEquipGift, createAdventureGift } from './rewards';
import { equipList } from './equip.js';

// 首先定义数据类型常量
const SceneType = {
    STORY: 'story',
    FIGHT: 'fight',
    ROAD: 'road',
    SHOP: 'shop', // Added
    NONE: 'none'
};

// 定义场景数据接口
const createScene = ({
    type = SceneType.NONE,
    id = 0,
    paraList = [],
    selectList = [],
    roadList = [],
    enemyList = [],
    giftList = [],
    to_id = null,
    treasureList = []
}) => ({
    type,
    id,
    paraList,
    selectList,
    roadList,
    enemyList,
    giftList,
    to_id,
    treasureList
});

// 定义商店场景数据接口
const createShopScene = ({
    id = 0,
    paraList = ['欢迎来到神秘商店！'],
    shopItems = [], // Array of item names (strings)
    to_id = null
}) => ({
    type: SceneType.SHOP,
    id,
    paraList,
    shopItems,
    to_id
});

// 添加一个辅助函数来获取随机宝物
function getRandomTreasures(level = 1, count = 3, toId = 2) {
    // 收集所有指定等级的装备和道具
    const allItems = [
        ...Object.entries(equipList)
            .filter(([key, item]) => item.level === level)
            .map(([key, item]) => ({
                text: item.desc,
                color: item.type === 'weapon' ? 'danger' : 'warning',
                to_id: toId,
                gift: createGift(key, GiftType.EQUIP)
            })),

    ];

    // 随机打乱数组
    const shuffled = allItems.sort(() => 0.5 - Math.random());
    
    // 返回指定数量的宝物
    return shuffled.slice(0, count);
}

// 添加一个函数来生成随机商店物品：2个武器、2个防具、1个护符
function getRandomShopItems() {
    const weapons = ['mujian_e', 'shengxiujian_e', 'xuantiejian_e', 'dagger_e', 'xixuejian_e', 'qiankunjian_e', 'taijidao_e'];
    const protectives = ['buyi_p', 'pijia_p', 'tiejia_p', 'xuantiejia_p', 'qiankunpao_p', 'taijijia_p'];
    const amulets = ['wulingzhu_a', 'shulaibao_a', 'shuqubao_a', 'datongin_a', 'datongin_b', 'yuwenjiangzhuang_a', 'zilaibao_a', 'ziqubao_a', 'gongbushiji_a', 'qisehua_a', 'shijianzha_a', 'chuancizhen_a'];
    
    // 随机选择2个武器
    const shuffledWeapons = weapons.sort(() => 0.5 - Math.random());
    const selectedWeapons = shuffledWeapons.slice(0, 2);
    
    // 随机选择2个防具
    const shuffledProtectives = protectives.sort(() => 0.5 - Math.random());
    const selectedProtectives = shuffledProtectives.slice(0, 2);
    
    // 随机选择1个护符
    const shuffledAmulets = amulets.sort(() => 0.5 - Math.random());
    const selectedAmulets = shuffledAmulets.slice(0, 1);
    
    return [...selectedWeapons, ...selectedProtectives, ...selectedAmulets];
}

// 将地图数据单独存放
const mapData = {
    1000: [
        createScene({
            type: SceneType.STORY,
            id: 0,
            paraList: [
                '你出现在一个傍晚。',
                '在一片大雾里你慢慢睁开眼。',
                '周围的一切你都感到很熟悉：身后的草垛、手边的木耙、远处的炊烟，以及————眼前的狼。呲着牙红着眼，弓着身子正欲冲上来饱餐一顿的，野狼。',
                '来不及多想，你决定：',
            ],
            selectList: [
                {
                    text: 'A: 向左跑去；',
                    to_id: 1,
                    gift: createGift('taibai_j', GiftType.SKILL)
                },
                {
                    text: 'B: 向右跑去；',
                    to_id: 2,
                    gift: createGift('jiuzhang_z', GiftType.SKILL)
                },
                {
                    text: 'C: 向后跑去；',
                    to_id: 3,
                    gift: createGift('qiyi_q', GiftType.SKILL)
                },
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 1,
            paraList: [
                '你顺势向左边一滚，站稳后急速地向左手边奔去。',
                '不料才跑了十余步，就一个踉跄被脚下的木桩绊倒了。',
                '再爬起来时手上不知何时多了一本古书。',
                '时光忽然慢了下来，你翻看了手上的书，文字古怪，你却读的毫无障碍，封面上写着四个古朴的大字：太白剑诀',
                '【获得《太白剑诀》！】',
            ],
            selectList: [
                {
                    text: '去战斗！！！',
                    to_id: 6,
                },
                {
                    text: '查看《太白剑诀》使用方法',
                    to_id: 4,
                }
            ],
        }),
        createScene({
            type: SceneType.STORY,
            id: 2,
            paraList: [
                '你顺势向右边一滚，站稳后急速地向右手边奔去。',
                '不料才跑了十余步，就一个踉跄被脚下的木桩绊倒了。',
                '再爬起来时手上不知何时多了一本古书。',
                '时光忽然慢了下来，你翻看了手上的书，文字古怪，你却读的毫无障碍，封面上写着四个古朴的大字：九章掌法',
                '【获得《九章掌法》！】',
            ],
            selectList: [
                {
                    text: '去战斗！！！',
                    to_id: 6,
                },
                {
                    text: '查看《九章掌法》使用方法',
                    to_id: 6,
                }
            ],
        }),
        createScene({
            type: SceneType.STORY,
            id: 3,
            paraList: [
                '你转身向后急速奔去，想要远离这头凶恶的野狼。',
                '不料才跑了十余步，就一个踉跄被脚下的木桩绊倒了。',
                '再爬起来时手上不知何时多了一本古书。',
                '时光忽然慢了下来，你翻看了手上的书，文字古怪，你却读的毫无障碍，封面上写着三个古朴的大字：七益拳',
                '【获得《七益拳》！】',
            ],
            selectList: [
                {
                    text: '去战斗！！！',
                    to_id: 6,
                },
                {
                    text: '查看《七益拳》使用方法',
                    to_id: 71,
                }
            ],
        }),
        // 4
        createScene({
            type: SceneType.STORY,
            id: 4,
            paraList: getParaByName('taibai_shouce'),
            selectList: [
                {
                    text: '去战斗！！！',
                    to_id: 6,
                },
            ],
        }),
        // 5
        createScene({
            type: SceneType.STORY,
            id: 5,
            paraList: [
                '走入黑暗中，你听到前方传来了呜呜的低鸣',
                '你小心翼翼的走上前去，发现映入眼帘的是一只垂垂老矣的老狼，它浑身毛发稀疏，眼神浑浊，仿佛随时都会油尽枯。',
                '它也看到了你，竟然用一种你能听得懂的语言开口了，它说：',
                '"我是被赶出狼群的旧狼王，你可以从眼前的三件宝物里挑选一件，拿起它，去帮我报仇吧！"',
            ],
            treasureList: getRandomTreasures(1, 3, 3)
        }),
        // 7
        createScene({
            type: SceneType.STORY,
            id: 71,
            paraList: getParaByName('qiyi_shouce'),
            selectList: [
                {
                    text: '去战斗！！！',
                    to_id: 6,
                },
            ],
        }),
        // 6 战斗
        createScene({
            type: SceneType.FIGHT,
            id: 6,
            to_id: 7,
            enemyList:[
                'yeyeyuan_yelang','yeyeyuan_yelang','yeyeyuan_yelang'
            ],
            giftList:[
                createRecoveryGift(0.2, 0.4),
                createAttributeGift(3, 5, 1),
                createProbabilityGift([
                    {
                        chance: 0.1,
                        name: 'jiuzhang_z',
                        type: GiftType.SKILL
                    },
                    {
                        chance: 0.1,
                        name: 'taibai_j',
                        type: GiftType.SKILL
                    }
                ], '技能盲盒', 3, 'danger'),
                createProbabilityGift([
                    {
                        chance: 0.1,
                        name: 'mujian_e',
                        type: GiftType.EQUIP
                    },
                    {
                        chance: 0.1,
                        name: 'mujian_e',
                        type: GiftType.EQUIP
                    }
                ], '护符盲盒', 4, 'warning')
            ]
        }),
        // 道路选择模块
        createScene({
            type: SceneType.ROAD,
            id: 7,
            paraList: [
                '杀死了野狼之后，你瘫坐在地上气喘吁吁，过了整整一刻钟的时间，你才第一次抬起来头来,仰望星空：',
                '夜已经深了，天上没有一颗星星，只有北边的空中挂着一颗硕大无比的月亮。',
                '那月亮是那么的大，那么的圆，让你不禁有一丝怀疑：这个巨大无比的白色光球，真的是月亮吗？',
                '你低下头去，环顾四周：东面的不远处是一片森林，炊烟从森林的方向袅袅升起，西面是看不到头的黑暗，南面和北面都是一片旷野，南面旷野的尽头是一座雪山，北面旷野的尽头是连绵不断的低矮山脉，月亮挂在山脉上方，照得山脉也泛着银光。',
                '你决定：(重要决定)',
            ],
            roadList: [
                {
                    text:'向东走去',
                    to_id:1100,
                    color:'primary',
                },
                {
                    text:'向西走去',
                    to_id:1200,
                    color:'danger',
                },
                {
                    text:'向南走去',
                    to_id:1300,
                    color:'warning',
                },
                {
                    text:'向北走去',
                    to_id:1400,
                    color:'success',
                },
            ],
            
        }),
    ],
    1100: [
        createScene({
            type: SceneType.STORY,
            id: 0,
            paraList: [
                '你朝着森林的方向慢慢走去。',
                '纵是深夜，皎洁的月亮也照的前方森林的轮廓清清楚楚的：那是普通的森林，白杨庄严，白桦高耸，橡树威风凛凛。还离着大老远就能听到风声穿过树枝间的沙沙作响。',
                '却没有一声鸟叫。',
                '"没有一声鸟叫"这个念头一出来，本来静谧祥和的气氛变得有点阴森。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 进入森林（新的挑战）；',
                    to_id: 2000,
                },
                {
                    text: 'B: 醒来；',
                    to_id: 10007, // Leads back to the main road selection
                },
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 1,
            paraList: [
                '你小心翼翼地走进森林。树木越来越密，月光被树冠遮住，四周越来越暗。突然，几根粗壮的藤蔓从地底钻出，向你袭来！',
                '【遭遇战：荆棘藤蔓！】',
            ],
            selectList: [
                {
                    text: '准备战斗',
                    to_id: 2,
                }
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 2,
            to_id: 3, // Leads to choice after battle
            enemyList: ['jingteng_normal', 'jingteng_normal', 'jingteng_normal'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 3,
            paraList: [
                '你击败了藤蔓。前方出现两条小径，都通往森林深处。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 走左边的小径；',
                    to_id: 4,
                },
                {
                    text: 'B: 走右边的小径；',
                    to_id: 5,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 4,
            to_id: 6, // Leads to choice after battle
            enemyList: ['jingteng_normal', 'jingteng_normal', 'jingteng_normal'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 5,
            to_id: 6, // Leads to choice after battle
            enemyList: ['jingteng_normal', 'jingteng_normal', 'jingteng_normal'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 6,
            paraList: [
                '你又击败了一波藤蔓。森林深处传来沙沙声，似乎有更多的植物被惊动了。你感到一阵不安。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 继续深入；',
                    to_id: 7,
                },
                {
                    text: 'B: 绕道而行；',
                    to_id: 8,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 7,
            to_id: 9, // Leads to choice after battle
            enemyList: ['jingteng_normal', 'jingteng_normal', 'jingteng_normal'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 8,
            to_id: 9, // Leads to choice after battle
            enemyList: ['jingteng_normal', 'jingteng_normal', 'jingteng_normal'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 9,
            paraList: [
                '你艰难地通过了藤蔓的阻碍。前方传来一阵恶臭，几朵巨大的食人花正摇曳着，似乎在等待猎物。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 正面迎战；',
                    to_id: 10,
                },
                {
                    text: 'B: 寻找弱点；',
                    to_id: 11,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 10,
            to_id: 12, // Leads to choice after battle
            enemyList: ['shirenhua_elite', 'shirenhua_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 11,
            to_id: 12, // Leads to choice after battle
            enemyList: ['shirenhua_elite', 'shirenhua_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 12,
            paraList: [
                '食人花被你击败了。你感到一阵眩晕，似乎吸入了它们散发的毒气。前方又出现了两朵食人花，它们看起来更加凶猛。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 强行突破；',
                    to_id: 13,
                },
                {
                    text: 'B: 迂回前进；',
                    to_id: 14,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 13,
            to_id: 15, // Leads to choice after battle
            enemyList: ['shirenhua_elite', 'shirenhua_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 14,
            to_id: 15, // Leads to choice after battle
            enemyList: ['shirenhua_elite', 'shirenhua_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 15,
            paraList: [
                '你终于击败了所有的食人花。森林深处传来一声震耳欲聋的咆哮。一棵巨大的古树拔地而起，它的树枝如同巨蟒般挥舞着，树干上长满了狰狞的眼睛。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 决一死战；',
                    to_id: 16, // Points to the shop
                },
                {
                    text: 'B: 寻找弱点；',
                    to_id: 16, // Points to the shop
                },
            ]
        }),
        // New Shop Scene for Forest Boss
        createShopScene({
            id: 16,
            paraList: [
                '在古树前，你发现了一个神秘的商店。',
                '一位神秘的商人坐在古树下，面前摆放着各种闪闪发光的装备。',
                '"欢迎，勇敢的冒险者！"商人微笑着说道，"我这里有最好的装备，能帮助你在接下来的战斗中获得胜利。"',
                '你可以在这里补充物资，为即将到来的挑战做好准备。'
            ],
            shopItems: getRandomShopItems(),
            to_id: 17 // Leads to the actual boss fight
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 17, // Shifted from 16
            to_id: 19, // Leads to end story (shifted from 18)
            enemyList: ['gushu_boss'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 18, // Shifted from 17
            to_id: 19, // Leads to end story (shifted from 18)
            enemyList: ['gushu_boss'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 19, // Shifted from 18
            paraList: [
                '你击败了古树守护者，森林恢复了平静。阳光穿透树冠，洒落在你身上。你感到一阵疲惫，但内心充满了胜利的喜悦。',
                '你终于走出了这片充满危机的森林。',
            ],
            selectList: [
                {
                    text: '返回夜夜原',
                    to_id: 10007, // This will lead back to the main road selection
                }
            ]
        }),
    ],
    // 西边地图（狼王）
    1200: [
        createScene({
            type: SceneType.STORY,
            id: 0,
            paraList: [
                '你朝着黑暗的方向慢慢走去。',
                '此起彼伏的低吼声从前方一声声传来。',
                '月光照亮了前方的一片空地，几双发着绿光的眼睛在黑暗中若隐若现。',
                '你意识到自己可能闯入了狼群的领地。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 悄悄后退；',
                    to_id: 25, // Leads to the end of the western path (shifted from 24)
                },
                {
                    text: 'B: 迎战（新的挑战）；',
                    to_id: 3000,
                },
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 1,
            paraList: [
                '你屏住呼吸，小心翼翼地向后退去。但是已经太迟了...狼群已经发现了你！',
                '【遭遇战：野狼！】',
            ],
            selectList: [
                {
                    text: '准备战斗',
                    to_id: 2,
                }
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 2,
            to_id: 3, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 3,
            paraList: [
                '你击败了第一波野狼。前方出现两条小径，都通往黑暗深处。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 走左边的小径；',
                    to_id: 4,
                },
                {
                    text: 'B: 走右边的小径；',
                    to_id: 5,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 4,
            to_id: 6, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 5,
            to_id: 6, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 6,
            paraList: [
                '你又击败了一波野狼。更多的狼嚎声从四面八方传来。你被包围了！',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 继续深入；',
                    to_id: 7,
                },
                {
                    text: 'B: 寻找突破口；',
                    to_id: 8,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 7,
            to_id: 9, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 8,
            to_id: 9, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 9,
            paraList: [
                '你已经筋疲力尽，但狼群依然紧追不舍。你必须坚持下去！',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 拼死一搏；',
                    to_id: 10,
                },
                {
                    text: 'B: 寻找掩护；',
                    to_id: 11,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 10,
            to_id: 12, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 11,
            to_id: 12, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 12,
            paraList: [
                '你又击败了一波野狼，但狼群似乎无穷无尽。你感到体力正在迅速流失。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 强行突围；',
                    to_id: 13,
                },
                {
                    text: 'B: 寻找出路；',
                    to_id: 14,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 13,
            to_id: 15, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 14,
            to_id: 15, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelang', 'yeyeyuan_yelang', 'yeyeyuan_yelang'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 15,
            paraList: [
                '你终于击退了大部分野狼，但前方出现了两只体型更大的狼，它们眼中闪烁着凶狠的光芒。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 正面迎战；',
                    to_id: 16,
                },
                {
                    text: 'B: 迂回前进；',
                    to_id: 17,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 16,
            to_id: 18, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelangplus', 'yeyeyuan_yelangplus'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 17,
            to_id: 18, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelangplus', 'yeyeyuan_yelangplus'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 18,
            paraList: [
                '你击败了第一批狼头目。更多的狼嚎声从远处传来，似乎是它们的同伴被惊动了。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 强行突破；',
                    to_id: 19,
                },
                {
                    text: 'B: 寻找弱点；',
                    to_id: 20,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 19,
            to_id: 21, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelangplus', 'yeyeyuan_yelangplus'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 20,
            to_id: 21, // Leads to choice after battle
            enemyList: ['yeyeyuan_yelangplus', 'yeyeyuan_yelangplus'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 21,
            paraList: [
                '你终于击败了所有的狼头目。一股强大的气息从黑暗深处传来。一只体型巨大、毛发漆黑的狼王出现在你面前，它的眼中充满了愤怒和杀意。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 决一死战；',
                    to_id: 22, // Points to the shop
                },
                {
                    text: 'B: 智取；',
                    to_id: 22, // Points to the shop
                },
            ]
        }),
        // New Shop Scene for Wolf King Boss
        createShopScene({
            id: 22,
            paraList: [
                '在狼王巢穴前，你发现了一个神秘的商店。',
                '商店被一层淡淡的魔法光芒包围，似乎不受野兽的侵扰。',
                '"啊，又一位挑战者！"商人兴奋地说道，"狼王可不是好对付的，你需要更强的装备！"',
                '商人的眼中闪烁着智慧的光芒，"选择合适的装备，它们将是你战胜狼王的关键。"'
            ],
            shopItems: getRandomShopItems(),
            to_id: 23 // Leads to the actual boss fight
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 23, // Shifted from 22
            to_id: 25, // Leads to end story (shifted from 24)
            enemyList: ['yeyeyuan_yelangplus'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 24, // Shifted from 23
            to_id: 25, // Leads to end story (shifted from 24)
            enemyList: ['yeyeyuan_yelangplus'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 25, // Shifted from 24
            paraList: [
                '你击败了狼王，狼群四散而逃。你感到一阵虚脱，但你成功地保卫了自己。',
                '你终于走出了这片充满危机的黑暗。',
            ],
            selectList: [
                {
                    text: '返回夜夜原',
                    to_id: 10007, // This will lead back to the main road selection
                }
            ]
        }),
    ],

    // 南边地图（雪山）
    1300: [
        createScene({
            type: SceneType.STORY,
            id: 0,
            paraList: [
                '你朝着雪山的方向走去。寒风凛冽，雪花飞舞。你感到一股强大的寒意袭来。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 沿着山路向上（新的挑战）；',
                    to_id: 4000,
                },
                {
                    text: 'B: 返回平原；',
                    to_id: 10007, // Leads back to the main road selection
                },
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 1,
            paraList: [
                '你开始沿着山路攀登。随着海拔的升高，空气逐渐变得稀薄。突然，几块冰晶从山壁上脱落，向你砸来！',
                '【遭遇战：雪山冰晶！】',
            ],
            selectList: [
                {
                    text: '准备战斗',
                    to_id: 2,
                }
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 2,
            to_id: 3, // Leads to choice after battle
            enemyList: ['xueshan_bingjing', 'xueshan_bingjing', 'xueshan_bingjing'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 3,
            paraList: [
                '你击碎了冰晶。前方出现两条小径，都通往雪山深处。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 走左边的小径；',
                    to_id: 4,
                },
                {
                    text: 'B: 走右边的小径；',
                    to_id: 5,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 4,
            to_id: 6, // Leads to choice after battle
            enemyList: ['xueshan_bingjing', 'xueshan_bingjing', 'xueshan_bingjing'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 5,
            to_id: 6, // Leads to choice after battle
            enemyList: ['xueshan_bingjing', 'xueshan_bingjing', 'xueshan_bingjing'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 6,
            paraList: [
                '你又击碎了一波冰晶。雪山深处传来一阵低吼，似乎有更多的冰雪生物被惊动了。你感到一阵刺骨的寒意。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 继续深入；',
                    to_id: 7,
                },
                {
                    text: 'B: 绕道而行；',
                    to_id: 8,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 7,
            to_id: 9, // Leads to choice after battle
            enemyList: ['xueshan_bingjing', 'xueshan_bingjing', 'xueshan_bingjing'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 8,
            to_id: 9, // Leads to choice after battle
            enemyList: ['xueshan_bingjing', 'xueshan_bingjing', 'xueshan_bingjing'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 9,
            paraList: [
                '你艰难地通过了冰晶的阻碍。前方的道路被两只巨大的雪怪堵住了。它们挥舞着巨大的手臂，向你冲来。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 正面迎战；',
                    to_id: 10,
                },
                {
                    text: 'B: 寻找弱点；',
                    to_id: 11,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 10,
            to_id: 12, // Leads to choice after battle
            enemyList: ['xueshan_xueguai', 'xueshan_xueguai'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 11,
            to_id: 12, // Leads to choice after battle
            enemyList: ['xueshan_xueguai', 'xueshan_xueguai'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 12,
            paraList: [
                '雪怪被你击败了。你感到一阵疲惫，似乎被它们的寒气侵蚀。前方又出现了两只雪怪，它们看起来更加凶猛。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 强行突破；',
                    to_id: 13,
                },
                {
                    text: 'B: 迂回前进；',
                    to_id: 14,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 13,
            to_id: 15, // Leads to choice after battle
            enemyList: ['xueshan_xueguai', 'xueshan_xueguai'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 14,
            to_id: 15, // Leads to choice after battle
            enemyList: ['xueshan_xueguai', 'xueshan_xueguai'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 15,
            paraList: [
                '你终于击败了所有的雪怪。雪山深处传来一声震耳欲聋的咆哮。一个巨大的雪神拔地而起，它的身体由冰雪构成，眼中闪烁着寒光。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 决一死战；',
                    to_id: 16, // Points to the shop
                },
                {
                    text: 'B: 智取；',
                    to_id: 16, // Points to the shop
                },
            ]
        }),
        // New Shop Scene for Snow Mountain Boss
        createShopScene({
            id: 16,
            paraList: [
                '在雪山之巅，你发现了一个神秘的商店。',
                '寒风呼啸中，商店散发着温暖的光芒，为这片冰雪世界带来一丝温暖。',
                '"在这严寒之地还能遇到客人，真是难得！"商人搓着手说道，"雪神可是冰雪的主宰，你需要特别的装备来对抗严寒。"',
                '"这些装备都经过了特殊的处理，能够抵御极寒的侵袭。"'
            ],
            shopItems: getRandomShopItems(),
            to_id: 17 // Leads to the actual boss fight
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 17, // Shifted from 16
            to_id: 19, // Leads to end story (shifted from 18)
            enemyList: ['xueshan_xueshen'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 18, // Shifted from 17
            to_id: 19, // Leads to end story (shifted from 18)
            enemyList: ['xueshan_xueshen'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 19, // Shifted from 18
            paraList: [
                '你击败了雪山雪神，雪山恢复了平静。阳光穿透云层，洒落在你身上。你感到一阵疲惫，但内心充满了胜利的喜悦。',
                '你终于走出了这片充满危机的雪山。',
            ],
            selectList: [
                {
                    text: '返回夜夜原',
                    to_id: 10007, // This will lead back to the main road selection
                }
            ]
        }),
    ],

    // 北边地图（往生井）
    1400: [
        createScene({
            type: SceneType.STORY,
            id: 0,
            paraList: [
                '你朝着群山的方向走去。月光洒在山脉上，形成一道银色的屏障。走近后你发现，山脚下有一口古井。井口刻着两个模糊的古字。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 查看古井（新的挑战）；',
                    to_id: 5000,
                },
                {
                    text: 'B: 绕过古井；',
                    to_id: 10007, // Leads back to the main road selection
                },
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 1,
            paraList: [
                '你走近古井。借着月光，你终于看清了井口的古字：往生。突然，几只幽魂从井口飘出，向你袭来！',
                '【遭遇战：往生幽魂！】',
            ],
            selectList: [
                {
                    text: '准备战斗',
                    to_id: 2,
                }
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 2,
            to_id: 3, // Leads to choice after battle
            enemyList: ['wangsheng_youhun', 'wangsheng_youhun', 'wangsheng_youhun'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 3,
            paraList: [
                '你击散了幽魂。更多的幽魂从井口涌出。你感到一阵阴冷。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 走左边的小径；',
                    to_id: 4,
                },
                {
                    text: 'B: 走右边的小径；',
                    to_id: 5,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 4,
            to_id: 6, // Leads to choice after battle
            enemyList: ['wangsheng_youhun', 'wangsheng_youhun', 'wangsheng_youhun'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 5,
            to_id: 6, // Leads to choice after battle
            enemyList: ['wangsheng_youhun', 'wangsheng_youhun', 'wangsheng_youhun'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 6,
            paraList: [
                '你又击散了一波幽魂。古井深处传来一阵恶臭，几只巨大的恶鬼正向你逼近。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 继续深入；',
                    to_id: 7,
                },
                {
                    text: 'B: 绕道而行；',
                    to_id: 8,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 7,
            to_id: 9, // Leads to choice after battle
            enemyList: ['wangsheng_youhun', 'wangsheng_youhun', 'wangsheng_youhun'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 8,
            to_id: 9, // Leads to choice after battle
            enemyList: ['wangsheng_youhun', 'wangsheng_youhun', 'wangsheng_youhun'],
            giftList: [
                createRecoveryGift(0.1, 0.2),
                createAttributeGift(1, 1, 0),
                createSkillGift(0.05, 1),
                createEquipGift(0.05, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 9,
            paraList: [
                '你艰难地通过了幽魂的阻碍。前方的道路被两只巨大的恶鬼堵住了。它们张牙舞爪，向你冲来。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 正面迎战；',
                    to_id: 10,
                },
                {
                    text: 'B: 寻找弱点；',
                    to_id: 11,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 10,
            to_id: 12, // Leads to choice after battle
            enemyList: ['wangsheng_egui', 'wangsheng_egui', 'bei_guiwang_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 11,
            to_id: 12, // Leads to choice after battle
            enemyList: ['wangsheng_egui', 'wangsheng_egui', 'bei_guishen_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 12,
            paraList: [
                '恶鬼被你击败了。你感到一阵眩晕，似乎被它们的邪气侵蚀。前方又出现了两只恶鬼，它们看起来更加凶猛。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 强行突破；',
                    to_id: 13,
                },
                {
                    text: 'B: 迂回前进；',
                    to_id: 14,
                },
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 13,
            to_id: 15, // Leads to choice after battle
            enemyList: ['wangsheng_egui', 'wangsheng_egui', 'bei_guizhu_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 14,
            to_id: 15, // Leads to choice after battle
            enemyList: ['wangsheng_egui', 'wangsheng_egui', 'bei_guiwang_elite'],
            giftList: [
                createRecoveryGift(0.2, 0.3),
                createAttributeGift(2, 2, 1),
                createSkillGift(0.1, 1),
                createEquipGift(0.1, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 15,
            paraList: [
                '你终于击败了所有的恶鬼。古井深处传来一声震耳欲聋的咆哮。一个巨大的亡灵从井口飘出，它的身体由黑雾构成，眼中闪烁着红光。',
                '你决定：',
            ],
            selectList: [
                {
                    text: 'A: 决一死战；',
                    to_id: 16, // Points to the shop
                },
                {
                    text: 'B: 智取；',
                    to_id: 16, // Points to the shop
                },
            ]
        }),
        // New Shop Scene for Well Boss
        createShopScene({
            id: 16,
            paraList: [
                '在古井深处，你发现了一个神秘的商店。',
                '井底的商店被古老的符文照亮，空气中弥漫着神秘的力量。',
                '"欢迎来到地底世界！"商人的声音在井壁间回响，"井神掌控着大地的力量，你需要坚固的装备来面对它。"',
                '"这些装备都蕴含着大地的祝福，能够增强你的防御和力量。"'
            ],
            shopItems: getRandomShopItems(),
            to_id: 17 // Leads to the actual boss fight
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 17, // Shifted from 16
            to_id: 19, // Leads to end story (shifted from 18)
            enemyList: ['wangsheng_wangling'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.FIGHT,
            id: 18, // Shifted from 17
            to_id: 19, // Leads to end story (shifted from 18)
            enemyList: ['wangsheng_wangling'],
            giftList: [
                createRecoveryGift(0.5, 0.8),
                createAttributeGift(5, 5, 3),
                createSkillGift(0.2, 1),
                createEquipGift(0.2, 1)
            ]
        }),
        createScene({
            type: SceneType.STORY,
            id: 19, // Shifted from 18
            paraList: [
                '你击败了往生亡灵，古井恢复了平静。阳光穿透云层，洒落在你身上。你感到一阵疲惫，但内心充满了胜利的喜悦。',
                '你终于走出了这片充满危机的古井。',
            ],
            selectList: [
                {
                    text: '返回夜夜原',
                    to_id: 10007, // This will lead back to the main road selection
                }
            ]
        }),
    ],
    // Main road selection scene for returning from other maps
    10007: [
        createScene({
            type: SceneType.ROAD,
            id: 0,
            paraList: [
                '你回到了夜夜原。',
                '夜已经深了，天上没有一颗星星，只有北边的空中挂着一颗硕大无比的月亮。',
                '那月亮是那么的大，那么的圆，让你不禁有一丝怀疑：这个巨大无比的白色光球，真的是月亮吗？',
                '你低下头去，环顾四周：东面的不远处是一片森林，炊烟从森林的方向袅袅升起，西面是看不到头的黑暗，南面和北面都是一片旷野，南面旷野的尽头是一座雪山，北面旷野的尽头是连绵不断的低矮山脉，月亮挂在山脉上方，照得山脉也泛着银光。',
                '你决定：(重要决定)',
            ],
            roadList: [
                {
                    text:'向东走去',
                    to_id:1100,
                    color:'primary',
                },
                {
                    text:'向西走去',
                    to_id:1200,
                    color:'danger',
                },
                {
                    text:'向南走去',
                    to_id:1300,
                    color:'warning',
                },
                {
                    text:'向北走去',
                    to_id:1400,
                    color:'success',
                },
            ],
        }),
    ]
};

// 动态生成关卡的函数
// 中文敌人名称到英文case的映射
function getEnemyKey(chineseName) {
    const enemyMapping = {
        // 东方森林 - 植物系
        '小草': 'dong_xiaocao',
        '竹子': 'dong_zhuzi', 
        '小花': 'dong_xiaohua',
        '大树': 'dong_dashu',
        '野丛': 'dong_yecong',
        '大树精': 'dong_dashu_elite',
        '花妖': 'dong_huayao_elite',
        '树王': 'dong_shuwang_elite',
        
        // 西方荒野 - 动物系
        '小虎': 'xi_xiaohu',
        '野牛': 'xi_yeniu',
        '火狸': 'xi_huoli',
        '山兔': 'xi_shantu',
        '青狼': 'xi_yelong',
        '暗影狼': 'xi_anyinglang_elite',
        '炽焰狼': 'xi_chiyanlang_elite',
        '夜夜狼': 'xi_yeyelang_elite',
        
        // 南方雪山 - 冰雪系
        '雪人': 'nan_xueren',
        '冰块': 'nan_bingkuai',
        '雪蜂': 'nan_xuefeng',
        '冰柱': 'nan_bingzhu',
        '雪球': 'nan_xuequiu',
        '冰王': 'nan_bingwang_elite',
        '雪王': 'nan_xuewang_elite',
        '冰雪王': 'nan_bingxue_elite',
        
        // 北方幽谷 - 幽灵系
        '小鬼': 'bei_xiaogui',
        '游魂': 'bei_youhun',
        '野鬼': 'bei_yegui',
        '恶鬼': 'bei_egui',
        '鬼影': 'bei_guiying',
        '鬼王': 'bei_guiwang_elite',
        '鬼神': 'bei_guishen_elite',
        '鬼主': 'bei_guizhu_elite'
    };
    
    return enemyMapping[chineseName] || chineseName;
}

function generateDynamicStages(direction, startId = 1000) {
    const stages = [];
    const directionConfig = {
        east: {
            name: '森林',
            normalEnemies: ['小草', '竹子', '小花', '大树', '野丛'],
            eliteEnemies: ['大树精', '花妖', '树王'],
            boss: 'gushu_boss',
            theme: '植物'
        },
        west: {
            name: '荒野',
            normalEnemies: ['小虎', '野牛', '火狸', '山兔', '青狼'],
            eliteEnemies: ['暗影狼', '炽焰狼', '夜夜狼'],
            boss: 'langwang_boss',
            theme: '动物'
        },
        south: {
            name: '雪山',
            normalEnemies: ['雪人', '冰块', '雪蜂', '冰柱', '雪球'],
            eliteEnemies: ['冰王', '雪王', '冰雪王'],
            boss: 'xueshanshen_boss',
            theme: '冰雪'
        },
        north: {
            name: '幽谷',
            normalEnemies: ['小鬼', '游魂', '野鬼', '恶鬼', '鬼影'],
            eliteEnemies: ['鬼王', '鬼神', '鬼主'],
            boss: 'wangsheng_boss',
            theme: '幽灵'
        }
    };
    
    const config = directionConfig[direction];
    if (!config) return [];
    
    let currentId = startId;
    
    // 生成30个战斗关卡
    for (let stage = 1; stage <= 30; stage++) {
        // 计算当前关卡的怪物数量（每10关增加一队敌人，初始3个）
        const baseMonsters = 3;
        const additionalTeams = Math.floor((stage - 1) / 10);
        const totalMonsters = baseMonsters + (additionalTeams * 3);
        
         // 根据关卡数动态调整精英怪概率：初始10%，每关增加1%
        let eliteChance = 0.1 + (stage - 1) * 0.01; // 第1关10%，第2关11%，以此类推
        // 确保概率不超过100%
        eliteChance = Math.min(eliteChance, 1.0);
        
        // 生成怪物列表
        const enemies = [];
        
        for (let i = 0; i < totalMonsters; i++) {
            const isElite = Math.random() < eliteChance;
            
            if (isElite) {
                // 生成精英怪物
                const randomElite = config.eliteEnemies[Math.floor(Math.random() * config.eliteEnemies.length)];
                enemies.push(getEnemyKey(randomElite));
            } else {
                // 生成普通怪物
                const randomEnemy = config.normalEnemies[Math.floor(Math.random() * config.normalEnemies.length)];
                enemies.push(getEnemyKey(randomEnemy));
            }
        }
        
        // 创建路径选择场景（除了第30关）
        if (stage < 30) {
            const storyId = currentId++;
            const leftFightId = currentId++;
            const rightFightId = currentId++;
            let nextStoryId;
            
            // 计算下一个场景的ID
            if (stage % 5 === 0) {
                // 如果是第5、10、15...关，下一个是商店
                const shopId = currentId++;
                nextStoryId = currentId; // 商店后的下一个故事场景
            } else if (stage % 5 === 3) {
                // 如果是第3、8、13...关，下一个是奇遇页面
                const adventureId = currentId++;
                nextStoryId = currentId; // 奇遇后的下一个故事场景
            } else {
                // 否则直接到下一个故事场景
                nextStoryId = currentId;
            }
            
            stages.push(createScene({
                type: SceneType.STORY,
                id: storyId,
                paraList: [
                    `你在${config.name}中前进，第${stage}个挑战出现在眼前。`,
                    `前方有两条路径可以选择：`,
                ],
                selectList: [
                    {
                        text: 'A: 选择左边的路径',
                        to_id: leftFightId,
                    },
                    {
                        text: 'B: 选择右边的路径',
                        to_id: rightFightId,
                    },
                ]
            }));
            
            // 左路径（正常怪物数量）
            let leftToId;
            if (stage % 5 === 0) {
                leftToId = leftFightId + 2; // 去商店
            } else if (stage % 5 === 3) {
                leftToId = leftFightId + 2; // 去奇遇页面
            } else {
                leftToId = nextStoryId; // 去下一个故事场景
            }
            
            stages.push(createScene({
                type: SceneType.FIGHT,
                id: leftFightId,
                to_id: leftToId,
                enemyList: enemies, // 显示所有计算出的怪物
                giftList: [
                    createRecoveryGift(0.1, 0.2),
                    createAttributeGift(1, 1, 0),
                    createSkillGift(0.05, 1),
                    createEquipGift(0.05, 1)
                ]
            }));
            
            // 右路径（50%概率减少1个怪物，50%概率增加1个怪物）
            let modifiedEnemies;
            if (Math.random() < 0.5) {
                // 50%概率减少1个怪物
                modifiedEnemies = enemies.length > 1 ? enemies.slice(0, -1) : enemies;
            } else {
                // 50%概率增加1个怪物
                const randomEnemy = config.normalEnemies[Math.floor(Math.random() * config.normalEnemies.length)];
                modifiedEnemies = [...enemies, getEnemyKey(randomEnemy)];
            }
            let rightToId;
            if (stage % 5 === 0) {
                rightToId = rightFightId + 1; // 去商店
            } else if (stage % 5 === 3) {
                rightToId = rightFightId + 1; // 去奇遇页面
            } else {
                rightToId = nextStoryId; // 去下一个故事场景
            }
            
            stages.push(createScene({
                type: SceneType.FIGHT,
                id: rightFightId,
                to_id: rightToId,
                enemyList: modifiedEnemies, // 显示修改后的所有怪物
                giftList: [
                    createRecoveryGift(0.1, 0.2),
                    createAttributeGift(1, 1, 0),
                    createSkillGift(0.05, 1),
                    createEquipGift(0.05, 1)
                ]
            }));
            
            // 每5关添加商店
            if (stage % 5 === 0) {
                stages.push(createShopScene({
                    id: rightFightId + 1,
                    paraList: [
                    `在${config.name}深处，你发现了一个神秘的商店。`,
                    '这里的空气中弥漫着神秘的气息，商店的装饰显得格外精致。',
                    '"看起来你已经走了很远的路程。"商人温和地说道，"休息一下，看看我的商品吧。"',
                    '"每一件装备都有它独特的力量，选择最适合你的那一件。"',
                    '"记得从商店后门出去哟~不然你会有大麻烦了"'
                ],
                    shopItems: getRandomShopItems(),
                    to_id: nextStoryId
                }));
            }
            
            // 每五关的第三关添加奇遇页面
            if (stage % 5 === 3) {
                stages.push(createScene({
                    type: SceneType.STORY,
                    id: rightFightId + 1,
                    paraList: [
                        `在${config.name}的深处，你遇到了一个神秘的奇遇！`,
                        `眼前出现了一个古老的宝箱，散发着神秘的光芒...`,
                        `快打开宝箱看看吧！`
                    ],
                    giftList: [createAdventureGift()],
                    selectList: [
                        {
                            text: '继续冒险',
                            to_id: nextStoryId
                        }
                    ]
                }));
            }
        } else {
            // 第30关（最终关卡）
            const storyId = currentId++;
            const leftFightId = currentId++;
            const rightFightId = currentId++;
            const shopId = currentId++;
            const bossId = currentId++;
            const endId = currentId++;
            
            // 第30关的路径选择
            stages.push(createScene({
                type: SceneType.STORY,
                id: storyId,
                paraList: [
                    `你在${config.name}中前进，最终的挑战出现在眼前。`,
                    `前方有两条路径通向最终的Boss战：`,
                ],
                selectList: [
                    {
                        text: 'A: 选择左边的路径',
                        to_id: leftFightId,
                    },
                    {
                        text: 'B: 选择右边的路径',
                        to_id: rightFightId,
                    },
                ]
            }));
            
            // 左路径（正常怪物数量）
            stages.push(createScene({
                type: SceneType.FIGHT,
                id: leftFightId,
                to_id: shopId,
                enemyList: enemies.slice(0, 3),
                giftList: [
                    createRecoveryGift(0.2, 0.3),
                    createAttributeGift(2, 2, 1),
                    createSkillGift(0.1, 1),
                    createEquipGift(0.1, 1)
                ]
            }));
            
            // 右路径（减少1个怪物）
            const reducedEnemies = enemies.length > 1 ? enemies.slice(0, -1) : enemies;
            stages.push(createScene({
                type: SceneType.FIGHT,
                id: rightFightId,
                to_id: shopId,
                enemyList: reducedEnemies.slice(0, 3),
                giftList: [
                    createRecoveryGift(0.2, 0.3),
                    createAttributeGift(2, 2, 1),
                    createSkillGift(0.1, 1),
                    createEquipGift(0.1, 1)
                ]
            }));
            
            // Boss前商店
            stages.push(createShopScene({
                id: shopId,
                paraList: [
                    `在${config.name}的最深处，你发现了最后的商店。`,
                    '这里的商人看起来比之前遇到的都要神秘，他的眼中闪烁着古老的智慧。',
                    '"终于有人走到了这里..."商人缓缓说道，"前方就是最终的挑战了。"',
                    '"这些是我最珍贵的收藏，它们将帮助你面对即将到来的终极考验。"',
                    '"选择吧，勇士，让命运决定你的道路！"'
                ],
                shopItems: getRandomShopItems(),
                to_id: bossId
            }));
            
            // Boss战
            stages.push(createScene({
                type: SceneType.FIGHT,
                id: bossId,
                to_id: endId,
                enemyList: [config.boss],
                giftList: [
                    createRecoveryGift(0.5, 0.8),
                    createAttributeGift(5, 5, 3),
                    createSkillGift(0.2, 1),
                    createEquipGift(0.2, 1)
                ]
            }));
            
            // 结束场景
            stages.push(createScene({
                type: SceneType.STORY,
                id: endId,
                paraList: [
                    `你击败了${config.name}的守护者，获得了胜利！`,
                    `${config.theme}的力量已经被你征服。`,
                ],
                selectList: [
                    {
                        text: '前往下一章',
                        to_id: 10007,
                    }
                ]
            }));
        }
    }
    
    return stages;
}

// 地图获取逻辑
export function getMapdata(mapIdx) {
    if (mapIdx === 1100) {
        return JSON.parse(JSON.stringify(generateDynamicStages('east', 2000)));
    } else if (mapIdx === 1200) {
        return JSON.parse(JSON.stringify(generateDynamicStages('west', 3000)));
    } else if (mapIdx === 1300) {
        return JSON.parse(JSON.stringify(generateDynamicStages('south', 4000)));
    } else if (mapIdx === 1400) {
        return JSON.parse(JSON.stringify(generateDynamicStages('north', 5000)));
    }
    
    // 对于静态地图数据，确保返回的是纯数组
    const staticMapData = mapData[mapIdx];
    if (staticMapData) {
        return JSON.parse(JSON.stringify(staticMapData));
    }
    
    return [createScene({})];
}
