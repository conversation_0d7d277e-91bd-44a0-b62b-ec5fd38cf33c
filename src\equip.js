// 装备列表
const equipList = {
    'none': {
        level: 0,
        name: '无',
        type: 'weapon',
        hurt: 0,
        desc: '没有武器',
        price: 0
    },
    'mupa_e': {
        level: 0,
        name: '木耙',
        type: 'weapon',
        hurt: 1,
        desc: '一把木耙',
        price: 5
    },
    'mujian_e': {
        level: 1,
        name: '木剑',
        type: 'weapon',
        hurt: 2,
        desc: '一把普通的木剑',
        price: 10
    },
    'shengxiujian_e': {
        level: 1,
        name: '生锈的铁剑',
        type: 'weapon',
        hurt: 5,
        desc: '一把生锈的铁剑',
        price: 20
    },
    'xuantiejian_e': {
        level: 2,
        name: '玄铁剑',
        type: 'weapon',
        hurt: 13,
        desc: '一把通体黢黑的玄铁剑',
        price: 50
    },
    'xuanyuanjian_e': {
        level: 10,
        name: '轩辕剑',
        type: 'weapon',
        hurt: 99,
        desc: '一把神剑',
        price: 5000
    },
    
    'yuemingzhu_p': {
        level: 10,
        name: '月明珠',
        type: 'prop',
        skill_type: 0,
        desc: '一颗泛着蓝光的宝珠',
        price: 100
    },
    'dagger_e': {
        level: 10,
        name: '狼王匕首',
        type: 'weapon',
        hurt: 25,
        desc: '一把锋利的匕首，散发着幽幽寒光',
        price: 300
    },
    'xixuejian_e': {
        level: 2,
        name: '嗜血剑',
        type: 'weapon',
        hurt: 8,
        lifesteal: 0.1,
        desc: '一把嗜血的魔剑，攻击时可吸取敌人生命力',
        price: 80
    },
    'xuanyinjian_e': {
        level: 3,
        name: '玄阴剑',
        type: 'weapon',
        hurt: 15,
        lifesteal: 0.15,
        desc: '传说中的魔剑，能够大量吸取敌人生命力',
        price: 200
    },
    // 新增武器 - 参考易经、老子、庄子、诗经、唐诗宋词
    'qiankunjian_e': {
        level: 4,
        name: '乾坤剑',
        type: 'weapon',
        hurt: 20,
        desc: '乾坤定位，阴阳交泰的神剑',
        price: 300
    },
    'taijidao_e': {
        level: 4,
        name: '太极刀',
        type: 'weapon',
        hurt: 18,
        lifesteal: 0.08,
        desc: '道生一，一生二，二生三，三生万物',
        price: 280
    },
    'xiaoyaobian_e': {
        level: 5,
        name: '逍遥鞭',
        type: 'weapon',
        hurt: 22,
        desc: '逍遥游于天地之间的神兵',
        price: 350
    },
    'guanheqiang_e': {
        level: 3,
        name: '关雎枪',
        type: 'weapon',
        hurt: 16,
        desc: '关关雎鸠，在河之洲',
        price: 220
    },
    'chunxiaojian_e': {
        level: 5,
        name: '春晓剑',
        type: 'weapon',
        hurt: 24,
        desc: '春眠不觉晓，处处闻啼鸟',
        price: 400
    },
    'mingyuefu_e': {
        level: 6,
        name: '明月斧',
        type: 'weapon',
        hurt: 28,
        desc: '举头望明月，低头思故乡',
        price: 480
    },
    'qingliandao_e': {
        level: 7,
        name: '青莲刀',
        type: 'weapon',
        hurt: 32,
        lifesteal: 0.12,
        desc: '青莲居士李太白的佩刀',
        price: 560
    },
    'baiyunbian_e': {
        level: 6,
        name: '白云鞭',
        type: 'weapon',
        hurt: 26,
        desc: '白云千载空悠悠',
        price: 450
    },
    'dongfengjian_e': {
        level: 8,
        name: '东风剑',
        type: 'weapon',
        hurt: 35,
        desc: '东风夜放花千树',
        price: 640
    },
    'luohongqiang_e': {
        level: 7,
        name: '落红枪',
        type: 'weapon',
        hurt: 30,
        desc: '落红不是无情物，化作春泥更护花',
        price: 520
    },
    'wuxingfu_e': {
        level: 9,
        name: '五行斧',
        type: 'weapon',
        hurt: 40,
        desc: '金木水火土，五行相生相克',
        price: 720
    },
    'bagua_dao_e': {
        level: 8,
        name: '八卦刀',
        type: 'weapon',
        hurt: 36,
        lifesteal: 0.10,
        desc: '八卦定吉凶，刀锋断阴阳',
        price: 600
    },
    'wuweijian_e': {
        level: 10,
        name: '无为剑',
        type: 'weapon',
        hurt: 45,
        desc: '无为而治，道法自然',
        price: 800
    },
    'zhuangzhoubian_e': {
        level: 9,
        name: '庄周鞭',
        type: 'weapon',
        hurt: 38,
        desc: '庄生晓梦迷蝴蝶',
        price: 680
    },
    'tiandifu_e': {
        level: 11,
        name: '天地斧',
        type: 'weapon',
        hurt: 50,
        desc: '天地玄黄，宇宙洪荒',
        price: 900
    },
    'yinyangdao_e': {
        level: 10,
        name: '阴阳刀',
        type: 'weapon',
        hurt: 42,
        lifesteal: 0.15,
        desc: '阴阳调和，万物生长',
        price: 750
    },
    'liushuijian_e': {
        level: 12,
        name: '流水剑',
        type: 'weapon',
        hurt: 55,
        desc: '上善若水，水善利万物而不争',
        price: 1000
    },
    'fengyueqiang_e': {
        level: 11,
        name: '风月枪',
        type: 'weapon',
        hurt: 48,
        desc: '春花秋月何时了，往事知多少',
        price: 850
    },
    'tiandaofu_e': {
        level: 15,
        name: '天道斧',
        type: 'weapon',
        hurt: 80,
        desc: '天道酬勤，厚德载物',
        price: 1500
    },
    // 防具装备
    'buyi_p': {
        level: 0,
        name: '布衣',
        type: 'protective',
        def: 1,
        hp_bonus: 2,
        desc: '普通的布制衣服，提供基础防护',
        price: 8
    },
    'pijia_p': {
        level: 1,
        name: '皮甲',
        type: 'protective',
        def: 3,
        hp_bonus: 5,
        desc: '用兽皮制成的护甲，轻便耐用',
        price: 25
    },
    'tiejia_p': {
        level: 2,
        name: '铁甲',
        type: 'protective',
        def: 6,
        hp_bonus: 10,
        desc: '厚重的铁制护甲，防护力强',
        price: 60
    },
    'xuantiejia_p': {
        level: 3,
        name: '玄铁甲',
        type: 'protective',
        def: 12,
        hp_bonus: 20,
        desc: '用玄铁锻造的护甲，坚不可摧',
        price: 150
    },
    // 新增防具 - 参考易经、老子、庄子、诗经、唐诗宋词
    'qiankunpao_p': {
        level: 4,
        name: '乾坤袍',
        type: 'protective',
        def: 15,
        hp_bonus: 25,
        desc: '乾坤定位的神袍，蕴含天地之力',
        price: 200
    },
    'taijijia_p': {
        level: 4,
        name: '太极甲',
        type: 'protective',
        def: 14,
        hp_bonus: 22,
        desc: '阴阳调和的护甲，道法自然',
        price: 180
    },
    'xiaoyaoyi_p': {
        level: 5,
        name: '逍遥衣',
        type: 'protective',
        def: 18,
        hp_bonus: 30,
        desc: '逍遥游天地的仙衣',
        price: 250
    },
    'guanheshan_p': {
        level: 3,
        name: '关雎衫',
        type: 'protective',
        def: 13,
        hp_bonus: 18,
        desc: '窈窕淑女，君子好逑',
        price: 160
    },
    'chunxiaopao_p': {
        level: 5,
        name: '春晓袍',
        type: 'protective',
        def: 20,
        hp_bonus: 35,
        desc: '春眠不觉晓的温暖长袍',
        price: 280
    },
    'mingyuejia_p': {
        level: 6,
        name: '明月甲',
        type: 'protective',
        def: 22,
        hp_bonus: 40,
        desc: '明月照我心的银色护甲',
        price: 320
    },
    'qinglianyi_p': {
        level: 7,
        name: '青莲衣',
        type: 'protective',
        def: 25,
        hp_bonus: 45,
        desc: '青莲居士的仙衣',
        price: 380
    },
    'baiyunshan_p': {
        level: 6,
        name: '白云衫',
        type: 'protective',
        def: 21,
        hp_bonus: 38,
        desc: '白云深处有人家',
        price: 300
    },
    'dongfengpao_p': {
        level: 8,
        name: '东风袍',
        type: 'protective',
        def: 28,
        hp_bonus: 50,
        desc: '东风夜放花千树的华丽长袍',
        price: 420
    },
    'luohongjia_p': {
        level: 7,
        name: '落红甲',
        type: 'protective',
        def: 24,
        hp_bonus: 42,
        desc: '落红护花的温柔护甲',
        price: 360
    },
    'wuxingyi_p': {
        level: 9,
        name: '五行衣',
        type: 'protective',
        def: 30,
        hp_bonus: 55,
        desc: '五行相生的神奇仙衣',
        price: 480
    },
    'baguapao_p': {
        level: 8,
        name: '八卦袍',
        type: 'protective',
        def: 27,
        hp_bonus: 48,
        desc: '八卦定乾坤的道袍',
        price: 400
    },
    'wuweijia_p': {
        level: 10,
        name: '无为甲',
        type: 'protective',
        def: 35,
        hp_bonus: 60,
        desc: '无为而治的至高护甲',
        price: 550
    },
    'zhuangzhouyi_p': {
        level: 9,
        name: '庄周衣',
        type: 'protective',
        def: 32,
        hp_bonus: 58,
        desc: '庄生晓梦的蝴蝶仙衣',
        price: 500
    },
    'tiandishan_p': {
        level: 11,
        name: '天地衫',
        type: 'protective',
        def: 38,
        hp_bonus: 65,
        desc: '天地玄黄的至尊衣衫',
        price: 600
    },
    'yinyangpao_p': {
        level: 10,
        name: '阴阳袍',
        type: 'protective',
        def: 36,
        hp_bonus: 62,
        desc: '阴阳调和的神袍',
        price: 580
    },
    'liushuijia_p': {
        level: 12,
        name: '流水甲',
        type: 'protective',
        def: 42,
        hp_bonus: 70,
        desc: '上善若水的柔韧护甲',
        price: 680
    },
    'fengyueyi_p': {
        level: 11,
        name: '风月衣',
        type: 'protective',
        def: 40,
        hp_bonus: 68,
        desc: '春花秋月的绝美仙衣',
        price: 650
    },
    'tiandaojia_p': {
        level: 15,
        name: '天道甲',
        type: 'protective',
        def: 60,
        hp_bonus: 100,
        desc: '天道酬勤的至高神甲',
        price: 1200
    },
    // 护符装备 - 可装备多个，提供各种属性加成
    'wulingzhu_a': {
        name: '无零珠',
        type: 'amulet',
        atk_bonus: 5,
        def_bonus: 3,
        special_effect: 'remove_zero_jiuzhang_boost',
        desc: '装备后，备选数字去掉0，九章章法伤害提升15%，可重复装配且伤害提升累加',
        price: 30
    },
    'shulaibao_a': {
        name: '数来宝',
        type: 'amulet',
        atk_bonus: 3,
        special_effect: 'jiuzhang_extra_slot',
        desc: '装备后，九章掌法填空数量+1，伤害提升20%，可重复装配且效果累加',
        price: 30
    },
    'shuqubao_a': {
        name: '数去宝',
        type: 'amulet',
        atk_bonus: -2,
        special_effect: 'jiuzhang_reduce_slot',
        desc: '装备后，九章掌法填空数量-1，伤害降低5%，可重复装配且效果累加（数字空不低于1个）',
        price: 30
    },
    'datongin_a': {
        name: '多面体',
        type: 'amulet',
        atk_bonus: 4,
        special_effect: 'jiuzhang_different_numbers_boost',
        desc: '装备后，九章掌法中，如果选择的数字各不相同，伤害增加20%，可重复装配且效果累加',
        price: 30
    },
    'datongin_b': {
        name: '大同印',
        type: 'amulet',
        atk_bonus: 4,
        special_effect: 'jiuzhang_same_numbers_boost',
        desc: '装备后，九章掌法使用中，如果选择的数字完全相同，伤害增加20%，可重复装配且效果累加',
        price: 30
    },
    'yuwenjiangzhuang_a': {
        name: '语文奖状',
        type: 'amulet',
        atk_bonus: 2,
        special_effect: 'taibai_perfect_boost',
        desc: '装备后，太白剑法填空全对时，伤害增加20%，可重复装配且效果累加',
        price: 30
    },
    'zilaibao_a': {
        name: '字来宝',
        type: 'amulet',
        atk_bonus: 3,
        special_effect: 'zilaibao_damage_boost',
        desc: '装备后，每装备一个太白剑法填空增加一个，技能伤害增加20%，可重复装配且效果累加',
        price: 30
    },
    'ziqubao_a': {
        name: '字去宝',
        type: 'amulet',
        atk_bonus: 1,
        special_effect: 'ziqubao_damage_reduce',
        desc: '装备后，每装备一个太白剑法填空减少一个，技能伤害下降5%，可重复装配且效果累加',
        price: 30
    },
    'gongbushiji_a': {
        name: '工部诗集',
        type: 'amulet',
        atk_bonus: 5,
        special_effect: 'gongbu_damage_boost',
        desc: '装备后,伤害提升30%，可选诗中增加杜甫诗作，可重复装配且效果累加',
        price: 50
    },
    'qisehua_a': {
        name: '七色花',
        type: 'amulet',
        atk_bonus: 3,
        special_effect: 'qiyi_extra_slot_damage_boost',
        desc: '装备后,七益拳填空数量+1，伤害提升25%，可重复装配且效果累加',
        price: 35
    },
    'shijianzha_a': {
        name: '时间炸弹',
        type: 'amulet',
        atk_bonus: 2,
        special_effect: 'time_bomb_damage_boost',
        desc: '装备后,答题结束剩余的时间可以转化为伤害，剩余的每一秒增加1%伤害，可重复装配且效果累加',
        price: 40
    },
    'chuancizhen_a': {
        name: '穿刺针',
        type: 'amulet',
        atk_bonus: 3,
        special_effect: 'pierce_damage_overflow',
        desc: '装备后,如果伤害杀死当前怪物还有剩余，剩余的伤害转嫁到下一个怪物上，可重复装配且效果累加',
        price: 50
    },
    'jubaopen_a': {
        name: '聚宝盆',
        type: 'amulet',
        atk_bonus: 2,
        special_effect: 'coin_boost_50',
        desc: '装备后,战斗结束获得的金币提升50%，可重复装配且效果累加',
        price: 50
    }
};

// 道具列表
// 将诗集类型的道具移到equipList中
equipList['canposhiji_p'] = {
    level: 1,
    name: '残破的诗集',
    desc: '一本残破的诗集',
    type: 'poem',
    poemName: 'canposhi',
    price: 40
};

equipList['gongbushiji_p'] = {
    level: 2,
    name: '工部诗集',
    desc: '一本稀有的诗集',
    type: 'poem',
    poemName: 'dufu',
    price: 80
};


// 获取装备或道具信息
export function getEquipByName(_name) {
    if (equipList.hasOwnProperty(_name)) {
        return equipList[_name];
    }
    // 如果都没找到，返回空装备
    return equipList.none;
}

// 导出列表
export { equipList };
