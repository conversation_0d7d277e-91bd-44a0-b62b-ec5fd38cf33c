<template>
  <div class="death-page">
    <!-- 背景遮罩 -->
    <div class="death-overlay"></div>
    
    <!-- 主要内容 -->
    <div class="death-content">
      <!-- 死亡标题 -->
      <div class="death-title">
        <h2>被击败-梦中惊醒</h2>
      </div>
      
      <!-- 故事文本 -->
      <div class="story-section">
        <div >
          <p>你猛地从梦中惊醒过来</p>
          <p>看着头顶上吱呀吱呀转着的电扇，一股困意又重新席卷而来</p>
          <p>在即将进入梦乡前，浑浑噩噩中，你看到眼前虚实之间浮现出一些物品</p>
        </div>
      </div>
      
      <!-- 装备选择区域 -->
      <div class="equipment-selection">
        <h3>从下面物品中选择一件</h3>
        
        <!-- 武器选择 -->
        <div class="equipment-category" v-if="availableWeapons.length > 0">
          <h4>武器</h4>
          <div class="equipment-grid">
            <div 
              v-for="weapon in availableWeapons" 
              :key="weapon.key"
              class="equipment-item"
              :class="{ selected: selectedEquipment === weapon.key }"
              @click="selectEquipment(weapon.key, 'weapon')"
            >
              <div class="equipment-icon">⚔️</div>
              <div class="equipment-name">{{ weapon.name }}</div>
              <div class="equipment-desc">{{ weapon.description }}</div>
            </div>
          </div>
        </div>
        
        <!-- 防具选择 -->
        <div class="equipment-category" v-if="availableArmor.length > 0">
          <h3>防具</h3>
          <div class="equipment-grid">
            <div 
              v-for="armor in availableArmor" 
              :key="armor.key"
              class="equipment-item"
              :class="{ selected: selectedEquipment === armor.key }"
              @click="selectEquipment(armor.key, 'armor')"
            >
              <div class="equipment-icon">🛡️</div>
              <div class="equipment-name">{{ armor.name }}</div>
              <div class="equipment-desc">{{ armor.description }}</div>
            </div>
          </div>
        </div>
        
        <!-- 护符选择 -->
        <div class="equipment-category" v-if="availableAmulets.length > 0">
          <h3>护符</h3>
          <div class="equipment-grid">
            <div 
              v-for="amulet in availableAmulets" 
              :key="amulet.key"
              class="equipment-item"
              :class="{ selected: selectedEquipment === amulet.key }"
              @click="selectEquipment(amulet.key, 'amulet')"
            >
              <div class="equipment-icon">🔮</div>
              <div class="equipment-name">{{ amulet.name }}</div>
              <div class="equipment-desc">{{ amulet.description }}</div>
            </div>
          </div>
        </div>
        
        <!-- 无装备选项 -->
        <div class="equipment-category">
          <div class="equipment-grid">
            <div 
              class="equipment-item"
              :class="{ selected: selectedEquipment === 'none' }"
              @click="selectEquipment('none', 'none')"
            >
              <div class="equipment-icon">✋</div>
              <div class="equipment-name">空手前行</div>
              <div class="equipment-desc">不带任何装备重新开始</div>
            </div>
          </div>
        </div>
      </div>
      

    </div>
  </div>
</template>

<script>
import { getEquipByName } from './equip.js'
import hero from './hero.js'

export default {
  name: 'DeathPage',
  props: {
    deadHero: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      selectedEquipment: null,
      selectedType: null
    }
  },
  computed: {
    // 获取可用的武器
    availableWeapons() {
      const weapons = []
      
      // 当前装备的武器
      if (this.deadHero.weapon && this.deadHero.weapon !== 'none') {
        const weaponData = getEquipByName(this.deadHero.weapon)
        if (weaponData) {
          weapons.push({
            key: this.deadHero.weapon,
            name: weaponData.name,
            description: weaponData.description || `攻击力+${weaponData.hurt || 0}`
          })
        }
      }
      
      // 背包中的武器
      this.deadHero.props.forEach(propKey => {
        const equipData = getEquipByName(propKey)
        if (equipData && equipData.type === 'weapon') {
          weapons.push({
            key: propKey,
            name: equipData.name,
            description: equipData.description || `攻击力+${equipData.hurt || 0}`
          })
        }
      })
      
      return weapons
    },
    
    // 获取可用的防具
    availableArmor() {
      const armor = []
      
      // 当前装备的防具
      if (this.deadHero.protective && this.deadHero.protective !== 'none') {
        const armorData = getEquipByName(this.deadHero.protective)
        if (armorData) {
          armor.push({
            key: this.deadHero.protective,
            name: armorData.name,
            description: armorData.description || `防御力+${armorData.def || 0}`
          })
        }
      }
      
      // 背包中的防具
      this.deadHero.props.forEach(propKey => {
        const equipData = getEquipByName(propKey)
        if (equipData && equipData.type === 'protective') {
          armor.push({
            key: propKey,
            name: equipData.name,
            description: equipData.description || `防御力+${equipData.def || 0}`
          })
        }
      })
      
      return armor
    },
    
    // 获取可用的护符
    availableAmulets() {
      const amulets = []
      
      // 当前装备的护符
      this.deadHero.amulets.forEach(amuletKey => {
        const amuletData = getEquipByName(amuletKey)
        if (amuletData) {
          amulets.push({
            key: amuletKey,
            name: amuletData.name,
            description: amuletData.description || '神秘的护符'
          })
        }
      })
      
      // 背包中的护符
      this.deadHero.props.forEach(propKey => {
        const equipData = getEquipByName(propKey)
        if (equipData && equipData.type === 'amulet') {
          amulets.push({
            key: propKey,
            name: equipData.name,
            description: equipData.description || '神秘的护符'
          })
        }
      })
      
      return amulets
    }
  },
  methods: {
    selectEquipment(equipmentKey, type) {
      this.selectedEquipment = equipmentKey
      this.selectedType = type
      // 选择后直接确认
      this.confirmSelection()
    },
    
    confirmSelection() {
      if (!this.selectedEquipment) return
      
      // 创建新英雄
      const newHero = new hero()
      
      // 如果选择了装备，则装备到新英雄身上
      if (this.selectedEquipment !== 'none') {
        const equipData = getEquipByName(this.selectedEquipment)
        if (equipData) {
          switch (this.selectedType) {
            case 'weapon':
              newHero.changeWapen(this.selectedEquipment)
              newHero.setATK(newHero.base_atk + (equipData.hurt || 0))
              break
            case 'armor':
              newHero.protective = this.selectedEquipment
              if (equipData.def) {
                newHero.setDEF(newHero.def + equipData.def)
              }
              if (equipData.hp_bonus) {
                newHero.setMaxHP(newHero.hp.max + equipData.hp_bonus)
                newHero.hp.value = newHero.hp.max
              }
              break
            case 'amulet':
              if (newHero.amulets.length < newHero.max_amulets) {
                newHero.amulets.push(this.selectedEquipment)
                // 应用护符属性加成
                if (equipData.atk_bonus) {
                  newHero.setATK(newHero.atk + equipData.atk_bonus)
                }
                if (equipData.def_bonus) {
                  newHero.setDEF(newHero.def + equipData.def_bonus)
                }
                if (equipData.hp_bonus) {
                  newHero.setMaxHP(newHero.hp.max + equipData.hp_bonus)
                  newHero.hp.value = newHero.hp.max
                }
                if (equipData.lifesteal_bonus) {
                  newHero.setLifesteal(newHero.lifesteal + equipData.lifesteal_bonus)
                }
                if (equipData.crit_bonus) {
                  newHero.baoji_rate += equipData.crit_bonus
                }
              }
              break
          }
        }
      }
      
      // 发送事件给父组件，传递新英雄和重新开始的信号
      this.$emit('restart-game', newHero)
    }
  }
}
</script>

<style scoped>
.death-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.death-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  opacity: 0.95;
}

.death-content {
  position: relative;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.95);
   border: 2px solid #87ceeb;
  border-radius: 15px;
  padding: 30px;
  color: #333;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

.death-title {
  text-align: center;
  margin-bottom: 30px;
}

.death-title h1 {
  font-size: 3em;
  color: #666;
  margin: 0;
  text-shadow: none;
  font-family: 'serif';
}

.death-subtitle {
  font-size: 1.2em;
  color: #888;
  margin-top: 10px;
  font-style: italic;
}

.story-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(250, 250, 250 0.5);
  border-radius: 10px;
  border-left: 4px solid #87ceeb;
}

.story-text p {
  font-size: 1.1em;
  line-height: 1.6;
  margin-bottom: 15px;
  color: #555;
}

.equipment-selection {
  margin-bottom: 30px;
}

.equipment-selection h2 {
  text-align: center;
  color: #666;
  margin-bottom: 25px;
  font-size: 1.5em;
}

.equipment-category {
  margin-bottom: 25px;
}

.equipment-category h3 {
  color: #777;
  margin-bottom: 15px;
  font-size: 1.2em;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.equipment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.equipment-item {
  background: rgba(248, 248, 248, 0.8);
   border: 2px solid #87ceeb;
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.equipment-item:hover {
  background: rgba(240, 240, 240, 0.9);
   border-color: #87ceeb;
  transform: translateY(-2px);
}

.equipment-item.selected {
  background: rgba(230, 255, 230, 0.8);
   border-color: #32cd32;
   box-shadow: 0 0 15px rgba(50, 205, 50, 0.3);
}

.equipment-icon {
  font-size: 2em;
  margin-bottom: 10px;
}

.equipment-name {
  font-weight: bold;
  color: #555;
  margin-bottom: 8px;
  font-size: 1.1em;
}

.equipment-desc {
  font-size: 0.9em;
  color: #777;
  line-height: 1.4;
}

.action-buttons {
  text-align: center;
}



/* 滚动条样式 */
.death-content::-webkit-scrollbar {
  width: 8px;
}

.death-content::-webkit-scrollbar-track {
  background: rgba(240, 240, 240, 0.5);
  border-radius: 4px;
}

.death-content::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.death-content::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}
</style>